# YuMi 双臂协作机器人 LEGO 城堡堆叠仿真项目任务书

## 1. 项目概述与最终目标

本项目旨在通过 MATLAB & Simulink 平台，实现 ABB YuMi 双臂机器人协作搭建一个复杂的八层 LEGO 城堡模型。核心目标是在 Simscape Multibody 环境中精确模拟机器人从拾取、搬运到最终堆叠 LEGO 积木的全过程，并确保最终成品与 `mainbu.ldr` 文件所定义的模型完全一致。

**关键成功标准:**
- **功能完整性:** Simulink 仿真需能完整、连续地展示从积木拾取到城堡搭建完成的全流程。
- **动作准确性:** 机器人手臂的运动轨迹、夹爪的开合时机必须准确无误。
- **结果一致性:** 最终堆叠的 LEGO 城堡结构需与 LeoCAD 设计图精确匹配。
- **数据可追溯:** 能够从仿真中记录并导出关节角度、末端执行器位姿、接触力等关键数据，用于后续分析与论文撰写。

---

## 2. 项目实施路线图 (Roadmap)

我们将项目分解为五个核心阶段，每个阶段包含若干具体任务。建议按顺序逐步完成，以确保项目的稳步推进。

### **阶段一：仿真环境初始化与机器人模型导入**

此阶段的目标是搭建一个稳定、可靠的 Simulink 仿真环境，并正确导入 YuMi 机器人模型。

- **✅ 任务 1.1：创建 Simulink 项目与模型**
  - [ ] 在 MATLAB 中创建一个新项目 (`.prj` 文件)，将所有相关文件（代码、模型、STL 网格等）添加至项目路径，以方便管理。
  - [ ] 创建一个新的 Simulink 模型，命名为 `Yumi_Lego_Simulation.slx`。

- **✅ 任务 1.2：导入 YuMi 机器人 URDF 模型**
  - [ ] 使用 MATLAB 命令 `smimport('abbYumi_exported.urdf')` 将 YuMi 的 URDF 文件转换为 Simscape Multibody 模型。
  - [ ] Simscape 会自动生成一个包含机器人所有连杆（Link）和关节（Joint）的 `.slx` 模型文件。将此模型内容复制并整合到我们的主仿真文件 `Yumi_Lego_Simulation.slx` 中。

- **✅ 任务 1.3：配置 Simscape 物理环境**
  - [ ] 在模型中添加并配置 `Mechanism Configuration` 模块，设置重力参数（例如 `[0 0 -9.81]`）。
  - [ ] 添加 `World Frame` 模块，作为整个仿真环境的全局绝对坐标系。
  - [ ] 添加 `Solver Configuration` 模块，确保 Simscape 求解器配置正确。

- **✅ 任务 1.4：解决坐标系不一致问题**
  - [ ] **核心任务!** 确定唯一的参考基准。建议以 `World Frame` 为绝对参考。
  - [ ] 使用 `Rigid Transform` 模块，在 `World Frame` 和 YuMi 机器人的基座 (`yumi_base_link`) 之间建立一个固定的坐标变换。这个变换的平移和旋转值需要精确测量和设定，以确保机器人在仿真世界中的位置和姿态是正确的。
  - [ ] 所有的 LEGO 积木坐标（无论是初始位置还是目标位置）都必须转换到 `World Frame` 下进行描述。

### **阶段二：LEGO 积木与工作场景建模**

此阶段专注于在仿真环境中创建 LEGO 积木和搭建平台。

- **✅ 任务 2.1：创建可复用的 LEGO 积木模块**
  - [ ] 创建一个新的 Subsystem，命名为 `LegoBrick`。
  - [ ] 在此 Subsystem 中，使用 `File Solid` 模块导入单个 LEGO 积木的 STL 模型（您需要有标准积木的 STL 文件）。
  - [ ] 为 `File Solid` 配置惯性（质量、惯量矩阵）、图形（颜色、透明度）和碰撞属性（接触刚度、阻尼）。这将直接解决您关于是否需要导入 CAD 的问题——答案是肯定的，为了精确的碰撞和可视化，必须使用 STL。
  - [ ] 在 `LegoBrick` 上添加 `6-DOF Joint`，使其可以自由地在空间中移动，直到被机器人抓取或放置。

- **✅ 任务 2.2：程序化生成初始积木堆**
  - [ ] 编写一个 MATLAB 脚本 (`setupLegoBricks.m`)，读取 `積木座標.csv.xlsx` 或 `第一層積木.csv.xlsx` 中的初始位置数据。
  - [ ] 在该脚本中，循环地将 `LegoBrick` Subsystem 的实例添加到主仿真模型中，并根据读取的坐标设置每个积木的初始位置。

- **✅ 任务 2.3：创建工作台**
  - [ ] 使用 `Brick Solid` 模块创建一个简单的立方体作为机器人和 LEGO 的工作台。
  - [ ] 确保工作台的位置和尺寸与您的物理设定（72x60 厘米）相匹配，并将其固定在 `World Frame` 上。

### **阶段三：双臂轨迹规划与数据接口**

此阶段的核心是解决 MATLAB 轨迹数据如何正确驱动 Simulink 模型的问题。

- **✅ 任务 3.1：确定并统一数据接口格式**
  - [ ] **核心任务!** 明确数据格式。我们将采用 `timeseries` 对象。
  - [ ] 对于**关节空间控制**，MATLAB 需要为每个手臂生成一个 `timeseries` 对象，其数据结构为 `[q1, q2, q3, q4, q5, q6, q7]`，代表七个关节随时间变化的期望角度。
  - [ ] 在 Simulink 中，使用 `From Workspace` 模块读取此 `timeseries` 对象。

- **✅ 任务 3.2：配置关节驱动**
  - [ ] 在 YuMi 模型的每个关节模块（Revolute Joint）中，将 `Actuation > Motion` 设置为 `Provided by Input`。
  - [ ] 将 `From Workspace` 的输出连接到 `Simulink-PS Converter` 模块，再连接到关节的输入端口，以驱动关节运动。确保信号单位正确（例如 rad 或 deg）。

- **✅ 任务 3.3：完善 MATLAB 轨迹生成逻辑 (`planTrajectory.m`)**
  - [ ] 脚本需要为**每一个动作**（例如：手臂1移动到积木A上方 -> 下降 -> 闭合夹爪 -> 上升 -> 移动到目标位置B上方 -> 下降 -> 松开夹爪 -> 上升）生成分段的五次多项式轨迹。
  - [ ] 将所有分段轨迹串联起来，形成一个完整的、连续的 `timeseries` 对象。
  - [ ] 同时，需要生成一个与轨迹时间同步的**夹爪控制信号**（见阶段四）。

- **✅ 任务 3.4：双臂任务分配与避障**
  - [ ] 在 MATLAB 中制定双臂任务分配策略。例如，左臂负责左侧区域的积木，右臂负责右侧区域。
  - [ ] 初步可以先通过规定双臂的工作空间和运动时序来避免碰撞。高级的 RRT 避障可以作为后续优化目标。

### **阶段四：夹爪 (Gripper) 控制与交互模拟**

此阶段实现 LEGO 的夹取和释放。

- **✅ 任务 4.1：创建夹爪控制信号**
  - [ ] 在 MATLAB 中，创建一个与主轨迹同步的 `timeseries` 信号。信号值为 `0` 代表张开，`1` 代表闭合。
  - [ ] 使用 `From Workspace` 模块将此信号导入 Simulink。

- **✅ 任务 4.2：模拟夹取物理过程**
  - [ ] 在夹爪手指和 LEGO 积木之间添加 `Spatial Contact Force` 模块。
  - [ ] 使用夹爪控制信号来控制这个接触力的启用/禁用，或者调整其刚度。当信号为 `1`（闭合）且手指与积木接触时，接触力生效，模拟夹紧效果。当信号为 `0`（张开）时，禁用接触力，释放积木。

- **✅ 任务 4.3：可视化夹爪动作**
  - [ ] 夹爪的连杆（`finger.stl`）本身就是模型的一部分。它们的开合动作也应由关节驱动。
  - [ ] 将夹爪控制信号转换为关节期望位置（例如，`0` 对应张开角度，`1` 对应闭合角度），并驱动夹爪的关节。

### **阶段五：系统集成、仿真与数据导出**

最后阶段，我们将所有模块整合，运行完整仿真，并收集结果。

- **✅ 任务 5.1：运行完整仿真**
  - [ ] 在 MATLAB 中运行主脚本 (`main.m`)，该脚本首先调用 `setupLegoBricks.m` 初始化场景，然后调用 `planTrajectory.m` 生成双臂和夹爪的完整轨迹数据。
  - [ ] 脚本最后使用 `sim('Yumi_Lego_Simulation.slx')` 命令启动仿真。

- **✅ 任务 5.2：调试与验证**
  - [ ] 从第一层开始，逐步仿真。观察机器人动作是否流畅，积木是否被准确夹取和放置。
  - [ ] 如果出现位置偏差、碰撞或逻辑错误，返回相应阶段进行调整。

- **✅ 任务 5.3：数据记录与导出**
  - [ ] 在 Simulink 模型中，使用 `To Workspace` 模块或 Scope 模块来监控和记录关键数据。
  - [ ] 记录每个关节的**实际**角度、速度（从关节模块的 `Sensing` 中引出）。
  - [ ] 记录 LEGO 积木的 6-DOF 位姿，以验证最终的堆叠精度。
  - [ ] 记录 `Spatial Contact Force` 模块输出的接触力，用于论文分析。

- **✅ 任务 5.4：扩展至完整八层城堡**
  - [ ] 在验证完单层逻辑后，扩展 `planTrajectory.m` 脚本，使其能够生成全部八层的搭建轨迹。
  - [ ] 运行最终的完整仿真，生成最终的成果视频和数据。

---

## 3. 关键问题应对策略
- **轨迹数据格式问题:** 严格采用 `[time, data]` 格式的 `timeseries` 对象，配合 `From Workspace` 模块。
- **Simulink 模块不熟:** 严格遵循本任务书和 MathWorks 链接中的经典结构：`From Workspace` -> `Joint Motion Input` / `Force Input`。
- **坐标系不一致:** 建立以 `World Frame` 为基准的唯一坐标系，所有物体的位置都在此坐标系下定义。
- **LEGO CAD 导入:** 必须使用 STL 文件并配置 `File Solid` 模块的物理和碰撞属性。

此任务书为您提供了从零开始到完成项目的清晰路径。让我们从阶段一的第一项任务开始吧！ 