%% YuMi LEGO 城堡堆叠仿真项目 - 环境验证脚本
% 验证 MATLAB 环境、工具箱和项目文件

fprintf('🚀 开始 YuMi LEGO 项目环境验证...\n\n');

%% 1. MATLAB 版本检查
fprintf('📋 步骤 1: MATLAB 版本检查\n');
fprintf('MATLAB 版本: %s\n', version);
matlab_version = version('-release');
fprintf('发布版本: %s\n', matlab_version);

% 检查版本是否满足要求 (R2023a 或更高)
required_year = 2023;
current_year = str2double(matlab_version(1:4));
if current_year >= required_year
    fprintf('✅ MATLAB 版本满足要求\n\n');
else
    fprintf('❌ MATLAB 版本过低，需要 R2023a 或更高版本\n\n');
end

%% 2. 必需工具箱检查
fprintf('📋 步骤 2: 工具箱检查\n');

% 检查 Robotics System Toolbox
if license('test', 'Robotics_System_Toolbox')
    fprintf('✅ Robotics System Toolbox: 已安装\n');
else
    fprintf('❌ Robotics System Toolbox: 未安装\n');
end

% 检查 Simscape Multibody
if license('test', 'Simscape_Multibody')
    fprintf('✅ Simscape Multibody: 已安装\n');
else
    fprintf('❌ Simscape Multibody: 未安装\n');
end

% 检查 Simulink
if license('test', 'Simulink')
    fprintf('✅ Simulink: 已安装\n');
else
    fprintf('❌ Simulink: 未安装\n');
end

fprintf('\n');

%% 3. 项目文件检查
fprintf('📋 步骤 3: 项目文件检查\n');

% 检查关键 MATLAB 文件
matlab_files = {
    'setupRobotEnv.m', '机器人环境设置';
    'planTrajectory.m', '轨迹规划';
    'lego_config.m', 'LEGO配置';
    'runSimulink.m', 'Simulink运行';
    'setup_lego_and_run_sim.m', '主仿真脚本'
};

for i = 1:size(matlab_files, 1)
    filename = matlab_files{i, 1};
    description = matlab_files{i, 2};
    if exist(filename, 'file')
        fprintf('✅ %s (%s): 存在\n', filename, description);
    else
        fprintf('❌ %s (%s): 缺失\n', filename, description);
    end
end

% 检查 Simulink 模型文件
simulink_files = {
    'YumiSimscape.slx', '主仿真模型';
    'abbYumi_exported.slx', 'YuMi导出模型'
};

for i = 1:size(simulink_files, 1)
    filename = simulink_files{i, 1};
    description = simulink_files{i, 2};
    if exist(filename, 'file')
        fprintf('✅ %s (%s): 存在\n', filename, description);
    else
        fprintf('❌ %s (%s): 缺失\n', filename, description);
    end
end

% 检查 URDF 文件
if exist('abbYumi_exported.urdf', 'file')
    fprintf('✅ abbYumi_exported.urdf (YuMi机器人模型): 存在\n');
else
    fprintf('❌ abbYumi_exported.urdf (YuMi机器人模型): 缺失\n');
end

% 检查数据文件
data_files = {
    '说明/積木座標.csv.xlsx', '积木坐标数据';
    '说明/第一層積木.csv.xlsx', '第一层积木数据';
    '说明/1.jpg', '城堡设计图'
};

for i = 1:size(data_files, 1)
    filename = data_files{i, 1};
    description = data_files{i, 2};
    if exist(filename, 'file')
        fprintf('✅ %s (%s): 存在\n', filename, description);
    else
        fprintf('❌ %s (%s): 缺失\n', filename, description);
    end
end

fprintf('\n');

%% 4. YuMi 机器人模型测试
fprintf('📋 步骤 4: YuMi 机器人模型测试\n');

try
    % 尝试加载 YuMi 机器人
    yumi = loadrobot('abbYumi', 'DataFormat', 'row', 'Gravity', [0 0 -9.81]);
    qHome = yumi.homeConfiguration;
    
    fprintf('✅ YuMi 机器人模型加载成功\n');
    fprintf('   - 机器人名称: %s\n', yumi.Name);
    fprintf('   - 关节数量: %d\n', yumi.NumBodies);
    fprintf('   - 自由度: %d\n', length(qHome));
    fprintf('   - 基座坐标: [%.3f, %.3f, %.3f]\n', yumi.Base(1,4), yumi.Base(2,4), yumi.Base(3,4));
    
    % 检查左右臂
    try
        T_left = getTransform(yumi, qHome, 'gripper_l_base');
        T_right = getTransform(yumi, qHome, 'gripper_r_base');
        fprintf('   - 左臂末端: [%.3f, %.3f, %.3f]\n', T_left(1,4), T_left(2,4), T_left(3,4));
        fprintf('   - 右臂末端: [%.3f, %.3f, %.3f]\n', T_right(1,4), T_right(2,4), T_right(3,4));
        fprintf('✅ 双臂运动学正常\n');
    catch ME
        fprintf('❌ 双臂运动学测试失败: %s\n', ME.message);
    end
    
catch ME
    fprintf('❌ YuMi 机器人模型加载失败: %s\n', ME.message);
end

fprintf('\n');

%% 5. Simulink 模型测试
fprintf('📋 步骤 5: Simulink 模型测试\n');

try
    % 检查主仿真模型
    if exist('YumiSimscape.slx', 'file')
        % 尝试加载模型但不打开
        load_system('YumiSimscape.slx');
        fprintf('✅ YumiSimscape.slx 模型加载成功\n');
        
        % 检查模型配置
        configSet = getActiveConfigSet('YumiSimscape');
        solver = get_param(configSet, 'Solver');
        fprintf('   - 求解器: %s\n', solver);
        
        % 关闭模型
        close_system('YumiSimscape', 0);
    else
        fprintf('❌ YumiSimscape.slx 模型文件不存在\n');
    end
catch ME
    fprintf('❌ Simulink 模型测试失败: %s\n', ME.message);
end

fprintf('\n');

%% 6. 内存和性能检查
fprintf('📋 步骤 6: 系统资源检查\n');

% 检查可用内存
[user, sys] = memory;
total_memory_gb = user.MemAvailableAllArrays / 1024^3;
fprintf('可用内存: %.1f GB\n', total_memory_gb);

if total_memory_gb >= 16
    fprintf('✅ 内存充足 (推荐 16GB+)\n');
elseif total_memory_gb >= 8
    fprintf('⚠️ 内存一般 (建议升级到 16GB+)\n');
else
    fprintf('❌ 内存不足 (需要至少 8GB)\n');
end

% 检查 MATLAB 路径
current_path = pwd;
fprintf('当前工作目录: %s\n', current_path);

fprintf('\n');

%% 7. 生成验证报告
fprintf('📋 步骤 7: 生成验证报告\n');

% 创建验证报告结构
verification_report = struct();
verification_report.timestamp = datestr(now);
verification_report.matlab_version = version;
verification_report.matlab_release = matlab_version;
verification_report.working_directory = current_path;
verification_report.available_memory_gb = total_memory_gb;

% 保存报告
save('环境验证报告.mat', 'verification_report');
fprintf('✅ 验证报告已保存: 环境验证报告.mat\n');

fprintf('\n');

%% 8. 总结和建议
fprintf('🎯 环境验证总结\n');
fprintf('=====================================\n');

% 检查关键条件
critical_checks = true;

% MATLAB 版本检查
if current_year < required_year
    fprintf('❌ 需要升级 MATLAB 到 R2023a 或更高版本\n');
    critical_checks = false;
end

% 工具箱检查
if ~license('test', 'Robotics_System_Toolbox')
    fprintf('❌ 需要安装 Robotics System Toolbox\n');
    critical_checks = false;
end

if ~license('test', 'Simscape_Multibody')
    fprintf('❌ 需要安装 Simscape Multibody\n');
    critical_checks = false;
end

if ~license('test', 'Simulink')
    fprintf('❌ 需要安装 Simulink\n');
    critical_checks = false;
end

% 文件检查
if ~exist('setupRobotEnv.m', 'file')
    fprintf('❌ 缺少关键文件: setupRobotEnv.m\n');
    critical_checks = false;
end

if critical_checks
    fprintf('🎉 环境验证通过！可以开始项目开发。\n');
    fprintf('\n📋 下一步建议:\n');
    fprintf('1. 运行 setupRobotEnv() 测试机器人环境\n');
    fprintf('2. 打开 YumiSimscape.slx 检查 Simulink 模型\n');
    fprintf('3. 开始阶段一任务: YuMi 机器人模型验证与优化\n');
else
    fprintf('⚠️ 环境验证未完全通过，请解决上述问题后重新验证。\n');
end

fprintf('\n🚀 YuMi LEGO 项目环境验证完成！\n');
