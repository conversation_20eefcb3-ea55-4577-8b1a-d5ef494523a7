function animateTrajectory(yumi, qHome, trajectories)
    % 創建新的動畫窗口，重用環境設置
    
    % 關閉之前的圖形（如果有的話）
    close all;
    
    % 重新設置環境
    fprintf('設置動畫環境...\n');
    [~, ~, ~, ax] = setupRobotEnv();
    
    % 更新圖形窗口標題
    set(gcf, 'Name', 'YuMi LEGO Trajectory Animate');
    
    % 顯示初始機器人配置
    show(yumi, qHome, 'PreservePlot', true, 'Frames', 'off', 'Parent', ax);
    hold on;

    % 軌跡線條顏色設定
    colors = struct('left', [1 0 0], 'right', [0 0 1]); % 紅色=左手，藍色=右手
 
 
    % === 動畫執行 ===
    
    % 動畫參數
    colors = struct('left', [1 0 0], 'right', [0 0 1]);
  % 動畫參數
    pauseTime = 0.05; % 軌跡繪製速度
    
    fprintf('\n=== 開始軌跡動畫 ===\n');
    total_trajectories = length(trajectories);
    total_points = sum(cellfun(@(t) size(t.Q,1), trajectories));
    
    fprintf('總軌跡數: %d\n', total_trajectories);
    fprintf('總點數: %d\n', total_points);
    fprintf('預估時間: %.1f 秒\n\n', total_points * pauseTime);
    
    current_point = 0;
    
    for i = 1:length(trajectories)
        traj = trajectories{i};
        armColor = colors.(traj.arm);
        traj_points = size(traj.Q, 1);
        
        fprintf('軌跡 %d: %s 手臂 (%d 點)\n', i, traj.arm, traj_points);
        
        for j = 1:traj_points
            current_point = current_point + 1;
            
            % 更新機器人配置
            show(yumi, traj.Q(j, :), 'PreservePlot', true, 'Frames', 'off', 'Parent', ax);
            
            % 計算總體進度
            total_progress = current_point / total_points * 100;
            traj_progress = j / traj_points * 100;
            
            % 更新標題
            title(sprintf('軌跡 %d/%d (%s 手臂) | 軌跡進度: %.1f%% | 總進度: %.1f%%', ...
                         i, total_trajectories, traj.arm, traj_progress, total_progress), ...
                         'FontSize', 12, 'FontWeight', 'bold');
            
            % 在命令窗口顯示進度（每10%更新一次）
            if mod(total_progress, 10) < 0.1 && mod(current_point, 10) == 0
                fprintf('  總進度: %.0f%%\n', total_progress);
            end
            
            drawnow;
            pause(pauseTime);
        end
        
        fprintf('  ✓ 軌跡 %d 完成\n', i);
    end
    
    % 最終狀態
    title('🎉 YuMi 雙臂軌跡動畫完成！', 'FontSize', 14, 'FontWeight', 'bold', 'Color', [0, 0.6, 0]);
    
    % 添加完成的視覺效果
    view(30, 20); % 稍微改變視角以展示最終結果
    
    fprintf('\n🎉 動畫完成！\n');
    fprintf('📊 統計信息:\n');
    fprintf('   - 總軌跡: %d\n', total_trajectories);
    fprintf('   - 總點數: %d\n', total_points);
    fprintf('   - 實際用時: %.1f 秒\n', total_points * pauseTime);
    
    % 保持窗口開啟
    fprintf('\n💡 提示: 窗口將保持開啟以查看最終結果\n');
end