function [sim_results] = enhanced_yumi_animation()
    % 增强版 YuMi LEGO 城堡堆叠动画仿真 (V3 - Final)
    % 包含完整的动画录制、数据驱动和健壮的IK处理
    
    fprintf('=== YuMi LEGO 城堡堆叠动画 (CSV驱动) ===\n\n');
    
    %% 1. 初始化参数
    fprintf('1. 初始化参数...\n');
    dt = 0.05; 
    t_total = 120;
    time_steps = 0:dt:t_total;
    n_steps = length(time_steps);
    video_filename = 'yumi_lego_simulation_final.mp4';
    frame_rate = 25;
    fprintf('✓ 参数初始化完成\n');

    %% 2. 加载环境和数据
    fprintf('\n2. 加载环境和数据...\n');
    try
        [yumi, qHome, ~, ax] = setupRobotEnv_fixed();
        fprintf('✓ 机器人环境加载成功\n');
    catch ME
        fprintf('✗ 机器人环境加载失败: %s\n', ME.message); return;
    end
    
    try
        opts = detectImportOptions('積木座標.csv(1).xlsx');
        opts.VariableNamingRule = 'preserve';
        lego_data = readtable('積木座標.csv(1).xlsx', opts);
        fprintf('✓ 积木坐标从 XLSX 文件加载成功\n');
    catch ME
        fprintf('✗ 无法加载积木坐标: %s\n', ME.message); return;
    end

    %% 3. 初始化LEGO积木 (数据驱动)
    fprintf('\n3. 初始化LEGO积木...\n');
    lego_data = lego_data(~ismissing(lego_data.("Brick ID")), :);
    num_blocks = height(lego_data) / 2;
    
    lego_blocks = struct();
    lego_blocks.count = num_blocks;
    lego_blocks.positions = zeros(num_blocks, 3, n_steps);
    lego_blocks.states = strings(num_blocks, n_steps);
    lego_blocks.ids = strings(num_blocks, 1);
    lego_blocks.sizes = zeros(num_blocks, 3);
    
    start_positions = zeros(num_blocks, 3);
    target_positions = zeros(num_blocks, 3);
    
    for i = 1:num_blocks
        start_row = lego_data(2*i-1, :);
        end_row = lego_data(2*i, :);
        
        lego_blocks.ids(i) = extractBefore(start_row.("Brick ID"){1}, '_start');
        brick_type_str = start_row.Type{1};
        lego_blocks.sizes(i, :) = parse_brick_type(brick_type_str);
        
        start_positions(i, :) = [start_row.('X (m)'), start_row.('Y (m)'), start_row.('Z (m)')];
        target_positions(i, :) = [end_row.('X (m)'), end_row.('Y (m)'), end_row.('Z (m)')];
        
        lego_blocks.positions(i, :, 1) = start_positions(i, :);
        lego_blocks.states(i, 1) = "free";
    end

    lego_blocks.colors = zeros(num_blocks, 3);
    for i = 1:num_blocks
        if start_positions(i, 2) > 0, lego_blocks.colors(i, :) = [0.9, 0.2, 0.2];
        else, lego_blocks.colors(i, :) = [0.2, 0.2, 0.9]; end
    end
    fprintf('✓ %d个积木初始化完成\n', num_blocks);

    %% 4. 创建任务序列
    fprintf('\n4. 创建任务序列...\n');
    task_sequence = create_task_sequence(start_positions, target_positions, lego_blocks.ids, lego_blocks.colors);
    fprintf('✓ %d个任务创建完成\n', length(task_sequence));

    %% 5. 创建动画环境
    fprintf('\n5. 创建动画环境...\n');
    fig = gcf; % Get current figure from setup
    block_handles = create_block_graphics(lego_blocks, ax);
    status_text = text(-0.1, -0.4, 0.5, '初始化...', 'FontSize', 14, 'FontWeight', 'bold', 'Parent', ax);
    title(ax, 'YuMi LEGO 动画 | 等待开始...', 'FontSize', 16);
    fprintf('✓ 动画环境创建完成\n');
    
    %% 6. 准备视频录制
    fprintf('\n6. 准备视频录制...\n');
    video_writer = VideoWriter(video_filename, 'MPEG-4');
    video_writer.FrameRate = frame_rate;
    open(video_writer);
    fprintf('✓ 视频录制准备就绪\n');

    %% 7. 主动画循环
    fprintf('\n7. 开始动画仿真...\n\n');
    ikLeft = inverseKinematics('RigidBodyTree', yumi, 'SolverAlgorithm', 'LevenbergMarquardt');
    ikRight = inverseKinematics('RigidBodyTree', yumi, 'SolverAlgorithm', 'LevenbergMarquardt');
    weights = [0.25 0.25 0.25 1 1 1];
    
    q_current = qHome'; % Ensure column vector
    
    for step = 2:n_steps
        current_time = time_steps(step);
        
        % --- 任务状态机 ---
        [task_sequence, active_task] = update_task_status(task_sequence, current_time);
        
        % --- 轨迹与IK计算 ---
        if ~isempty(active_task)
            progress = min(1, (current_time - active_task.start_time) / active_task.duration);
            block_size = lego_blocks.sizes(active_task.block_id, :);
            
            [T_gripper, current_block_pos] = calculate_trajectory(active_task.pick_pos, active_task.place_pos, progress, block_size);
            
            lego_blocks.positions(active_task.block_id, :, step) = current_block_pos;
            lego_blocks.states(active_task.block_id, step) = "moving";

            if strcmp(active_task.arm, 'left')
                [q_sol, solInfo] = ikLeft('gripper_l_base', T_gripper, weights, q_current');
                if strcmp(solInfo.Status, 'success'), q_current(1:7) = q_sol(1:7); end
            else
                [q_sol, solInfo] = ikRight('gripper_r_base', T_gripper, weights, q_current');
                if strcmp(solInfo.Status, 'success'), q_current(8:14) = q_sol(8:14); end
            end
        end
        
        % --- 状态继承 ---
        for i = 1:num_blocks
            if lego_blocks.states(i, step) == ""
            lego_blocks.positions(i, :, step) = lego_blocks.positions(i, :, step-1);
            lego_blocks.states(i, step) = lego_blocks.states(i, step-1);
            end
        end
        
        % --- 动画帧更新 ---
        update_animation_frame(fig, video_writer, yumi, q_current, lego_blocks, block_handles, step, status_text, task_sequence, current_time, active_task);
    end
    
    close(video_writer);
    save('final_simulation_results.mat', 'sim_results');
    saveas(fig, 'final_simulation_screenshot.png');
    fprintf('\n\n✅ 仿真完成! 视频已保存至 %s\n', video_filename);
end

% --- Helper Functions ---

function tasks = create_task_sequence(starts, targets, ids, colors)
    tasks = [];
    left_indices = find(starts(:, 2) > 0);
    right_indices = find(starts(:, 2) <= 0);
    
    task_order = [];
    max_len = max(length(left_indices), length(right_indices));
    for i = 1:max_len
        if i <= length(left_indices), task_order = [task_order, left_indices(i)]; end
        if i <= length(right_indices), task_order = [task_order, right_indices(i)]; end
    end

    for idx = 1:length(task_order)
        i = task_order(idx);
        task.block_id = i;
        task.arm = iif(starts(i, 2) > 0, "left", "right");
        task.pick_pos = starts(i, :);
        task.place_pos = targets(i, :);
        task.status = "pending";
        task.start_time = (idx-1) * 5.0;
        task.duration = 4.5;
        task.color = colors(i, :);
        task.id_str = ids(i);
        tasks = [tasks; task];
    end
end

function [task_sequence, active_task] = update_task_status(task_sequence, current_time)
    active_task = [];
    for i = 1:length(task_sequence)
        if strcmp(task_sequence(i).status, "pending") && current_time >= task_sequence(i).start_time
            task_sequence(i).status = "active";
        elseif strcmp(task_sequence(i).status, "active") && current_time >= (task_sequence(i).start_time + task_sequence(i).duration)
            task_sequence(i).status = "completed";
        end
        if strcmp(task_sequence(i).status, "active")
            active_task = task_sequence(i);
        end
    end
end

function [T_gripper, current_pos] = calculate_trajectory(start_pos, end_pos, progress, block_size)
    hover_h = 0.1; % Hover height
    p_grasp = 0.2; p_lift = 0.35; p_move = 0.85;

    is_attached = false;
    gripper_target = zeros(1,3);

    if progress <= p_grasp
        p = progress / p_grasp;
        gripper_target = (start_pos + [0 0 hover_h]) * (1-p) + start_pos * p;
    elseif progress <= p_lift
        is_attached = true;
        p = (progress - p_grasp) / (p_lift - p_grasp);
        gripper_target = start_pos * (1-p) + (start_pos + [0 0 hover_h]) * p;
    elseif progress <= p_move
        is_attached = true;
        p = (progress - p_lift) / (p_move - p_lift);
        gripper_target = (start_pos + [0 0 hover_h]) * (1-p) + (end_pos + [0 0 hover_h]) * p;
    else
        is_attached = true;
        p = (progress - p_move) / (1 - p_move);
        gripper_target = (end_pos + [0 0 hover_h]) * (1-p) + end_pos * p;
    end
    
    current_pos = iif(is_attached, gripper_target, start_pos);
    
    T_gripper = trvec2tform(gripper_target + [0 0 block_size(3)/2]);
    T_gripper(1:3, 1:3) = [1 0 0; 0 -1 0; 0 0 -1];
end

function handles = create_block_graphics(lego_blocks, ax)
    handles = [];
    for i = 1:lego_blocks.count
        pos = lego_blocks.positions(i, :, 1);
        [vertices, faces] = create_lego_block_mesh(lego_blocks.sizes(i, :));
        h = patch(ax, 'Vertices', vertices + pos, 'Faces', faces, 'FaceColor', lego_blocks.colors(i, :), 'EdgeColor', 'k', 'LineWidth', 1);
        handles = [handles; h];
    end
end

function update_animation_frame(fig, writer, robot, q, blocks, handles, step, status_h, tasks, time, active_task)
    persistent last_frame_time;
    if isempty(last_frame_time), last_frame_time = -1; end
    
    if time - last_frame_time < (1/writer.FrameRate), return; end

    show(robot, q', 'Visuals', 'on', 'Collisions', 'off', 'PreservePlot', false);
    
    for i = 1:blocks.count
        set(handles(i), 'Vertices', create_lego_block_mesh(blocks.sizes(i, :)) + blocks.positions(i, :, step));
    end
    
    if ~isempty(active_task)
        status_str = sprintf('时间: %.1fs | 执行中: %s臂 → %s', time, upper(active_task.arm), active_task.id_str);
    else
        status_str = sprintf('时间: %.1fs | 等待任务...', time);
    end
    set(status_h, 'String', status_str);
    
    completed_count = sum(arrayfun(@(x) strcmp(x.status, 'completed'), tasks));
    title(gca, sprintf('YuMi LEGO 动画 | 已完成: %d/%d', completed_count, blocks.count));
    
    drawnow;
    
    try
        frame = getframe(fig);
        writeVideo(writer, frame);
        last_frame_time = time;
    catch ME
        fprintf('视频帧写入失败: %s\n', ME.message);
    end
end

function size_vec = parse_brick_type(type_str)
    persistent stud_pitch brick_height;
    if isempty(stud_pitch)
        stud_pitch = 0.008; brick_height = 0.0096;
    end
    
    dims = sscanf(lower(type_str), '%dx%d');
    if length(dims) == 2
        w = dims(1) * stud_pitch - 0.0002;
        d = dims(2) * stud_pitch - 0.0002;
        size_vec = iif(contains(type_str, 'Vert', 'IgnoreCase', true), [d, w, brick_height], [w, d, brick_height]);
    else
        size_vec = [0.0318, 0.0158, 0.0096]; % Default
    end
end

function [v, f] = create_lego_block_mesh(s)
    l=s(1)/2; w=s(2)/2; h=s(3);
    v = [-l -w 0; l -w 0; l w 0; -l w 0; -l -w h; l -w h; l w h; -l w h];
    f = [1 2 3 4; 5 8 7 6; 1 5 6 2; 3 7 8 4; 1 4 8 5; 2 6 7 3];
end

function out = iif(cond, true_val, false_val)
    if cond, out = true_val; else, out = false_val; end
end 