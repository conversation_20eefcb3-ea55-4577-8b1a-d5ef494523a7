function [sim_results] = enhanced_yumi_animation()
    % 增强版 YuMi LEGO 城堡堆叠动画仿真
    % 包含完整的动画录制和视频输出功能
    
    fprintf('=== 增强版 YuMi LEGO 城堡堆叠动画仿真 ===\n\n');
    
    %% 1. 初始化参数
    fprintf('1. 初始化动画参数...\n');
    
    % 动画参数
    dt = 0.05;  % 时间步长 (50ms)
    t_total = 120; % 增加总时间以容纳所有8层
    time_steps = 0:dt:t_total;
    n_steps = length(time_steps);
    
    % 视频参数
    frame_rate = 20;
    video_filename = 'enhanced_yumi_lego_animation.mp4';
    
    fprintf('✓ 动画参数设置完成\n');
    
    %% 2. 加载机器人环境和CSV数据
    fprintf('\n2. 加载机器人和积木数据...\n');
    
    try
        [yumi, qHome, table, ax] = setupRobotEnv_fixed();
        fprintf('✓ 机器人环境加载成功\n');
    catch ME
        fprintf('✗ 机器人环境加载失败: %s\n', ME.message);
        return;
    end
    
    try
        % 从XLSX文件加载积木坐标
        opts = detectImportOptions('積木座標.csv(1).xlsx');
        opts.VariableNamingRule = 'preserve';
        lego_data = readtable('積木座標.csv(1).xlsx', opts);
        fprintf('✓ 积木坐标从 XLSX 文件加载成功\n');
    catch ME
        fprintf('✗ 无法加载积木坐标: %s\n', ME.message);
        return;
    end
    
    %% 3. 初始化LEGO积木
    fprintf('\n3. 初始化LEGO积木...\n');
    
    lego_data = lego_data(~ismissing(lego_data.("Brick ID")), :);
    
    num_blocks = height(lego_data) / 2;
    lego_blocks = struct();
    lego_blocks.count = num_blocks;
    lego_blocks.mass = 0.0024; % Mass is constant for now
    
    lego_blocks.positions = zeros(num_blocks, 3, n_steps);
    lego_blocks.states = strings(num_blocks, n_steps);
    lego_blocks.ids = strings(num_blocks, 1);
    lego_blocks.sizes = zeros(num_blocks, 3); % Add sizes field

    start_positions = zeros(num_blocks, 3);
    target_positions = zeros(num_blocks, 3);
    
    for i = 1:num_blocks
        start_row = lego_data(2*i-1, :);
        end_row = lego_data(2*i, :);
        
        lego_blocks.ids(i) = extractBefore(start_row.("Brick ID"){1}, '_start');
        
        % Get size from type
        brick_type = start_row.Type{1};
        lego_blocks.sizes(i, :) = parse_brick_type(brick_type);

        start_positions(i, :) = [start_row.('X (m)'), start_row.('Y (m)'), start_row.('Z (m)')];
        target_positions(i, :) = [end_row.('X (m)'), end_row.('Y (m)'), end_row.('Z (m)')];
        
        lego_blocks.positions(i, :, 1) = start_positions(i, :);
        lego_blocks.states(i, 1) = "free";
    end
    
    % 颜色配置 (基于左右臂)
    lego_blocks.colors = zeros(num_blocks, 3);
    for i = 1:num_blocks
        if start_positions(i, 2) > 0 % Y > 0 for Left arm
            lego_blocks.colors(i, :) = [0.9, 0.1, 0.1]; % 鲜红色
        else % Y <= 0 for Right arm
            lego_blocks.colors(i, :) = [0.1, 0.1, 0.9]; % 鲜蓝色
        end
    end
    
    fprintf('✓ LEGO积木初始化完成\n');
    fprintf('  - 积木总数: %d\n', lego_blocks.count);
    
    %% 4. 创建任务序列
    fprintf('\n4. 创建任务序列...\n');
    
    task_sequence = struct();
    task_sequence.tasks = [];
    
    % 简单的交替顺序
    left_tasks = find(start_positions(:, 2) > 0);
    right_tasks = find(start_positions(:, 2) <= 0);
    
    task_order = [];
    max_len = max(length(left_tasks), length(right_tasks));
    for i = 1:max_len
        if i <= length(left_tasks)
            task_order = [task_order, left_tasks(i)];
        end
        if i <= length(right_tasks)
            task_order = [task_order, right_tasks(i)];
        end
    end
    
    for idx = 1:length(task_order)
        i = task_order(idx);
        task = struct();
        task.block_id = i;
        
        if start_positions(i, 2) > 0
            task.arm = "left";
        else
            task.arm = "right";
        end
        
        task.pick_pos = start_positions(i, :);
        task.place_pos = target_positions(i, :);
        task.status = "pending";
        task.start_time = (idx-1) * 5.0;
        task.duration = 4.0;
        task.color = lego_blocks.colors(i, :);
        
        task_sequence.tasks = [task_sequence.tasks; task];
    end
    
    fprintf('✓ 任务序列创建完成 (%d个任务)\n', length(task_sequence.tasks));
    
    %% 5. 创建动画环境
    fprintf('\n5. 创建动画环境...\n');
    
    fig = figure('Name', 'YuMi LEGO 城堡堆叠动画 (CSV驱动)', ...
                 'Position', [100, 100, 1400, 900], 'Color', 'white');
    clf;
    
    [yumi, qHome, table, ax] = setupRobotEnv_fixed();
    hold on;
    
    view(45, 30);
    axis equal;
    grid on;
    lighting gouraud;
    light('Position', [1, 1, 1]);
    
    xlim([-0.2, 1.0]);
    ylim([-0.5, 0.5]); % 扩大Y轴范围
    zlim([-0.1, 0.6]);
    
    block_handles = [];
    text_handles = [];
    
    for i = 1:lego_blocks.count
        pos = lego_blocks.positions(i, :, 1);
        [vertices, faces] = create_lego_block_mesh(lego_blocks.sizes(i, :));
        vertices = vertices + pos;
        
        h = patch('Vertices', vertices, 'Faces', faces, ...
                  'FaceColor', lego_blocks.colors(i, :), 'EdgeColor', 'k', ...
                  'LineWidth', 1, 'FaceAlpha', 0.9);
        block_handles = [block_handles; h];
        
        ht = text(pos(1), pos(2), pos(3) + lego_blocks.sizes(i, 3) + 0.01, ...
                  lego_blocks.ids(i), 'HorizontalAlignment', 'center', ...
                  'FontSize', 8, 'FontWeight', 'bold');
        text_handles = [text_handles; ht];
    end
    
    status_text = text(-0.1, -0.4, 0.5, '', 'FontSize', 14, 'FontWeight', 'bold');
    
    title('YuMi LEGO 城堡堆叠动画 (CSV驱动)', 'FontSize', 16);
    
    fprintf('✓ 动画环境创建完成\n');
    
    %% 6. 准备视频录制
    fprintf('\n6. 准备视频录制...\n');
    video_writer = VideoWriter(video_filename, 'MPEG-4');
    video_writer.FrameRate = frame_rate;
    open(video_writer);
    fprintf('✓ 视频录制准备完成\n');
    
    %% 7. 主动画循环
    fprintf('\n7. 开始动画仿真...\n');
    
    frame_count = 0;
    last_frame_time = -inf;
    frame_interval = 1 / frame_rate;
    
    % 初始化IK求解器
    ikLeft = inverseKinematics('RigidBodyTree', yumi, 'SolverAlgorithm', 'LevenbergMarquardt');
    ikRight = inverseKinematics('RigidBodyTree', yumi, 'SolverAlgorithm', 'LevenbergMarquardt');
    weights = [0.25 0.25 0.25 1 1 1]; % 优先姿态
    initialGuess = qHome'; % 使用完整的机器人姿态作为初始猜测(列向量)

    q_current = initialGuess; % 存储当前机器人姿态

    for step = 2:n_steps
        current_time = time_steps(step);
        active_task_found = false;
        
        % Update task statuses first
        for i = 1:length(task_sequence.tasks)
            if strcmp(task_sequence.tasks(i).status, "pending") && current_time >= task_sequence.tasks(i).start_time
                task_sequence.tasks(i).status = "active";
            elseif strcmp(task_sequence.tasks(i).status, "active") && current_time >= (task_sequence.tasks(i).start_time + task_sequence.tasks(i).duration)
                task_sequence.tasks(i).status = "completed";
            end
        end

        % Process the active task
        for i = 1:length(task_sequence.tasks)
            if strcmp(task_sequence.tasks(i).status, "active")
                active_task_found = true;
                task = task_sequence.tasks(i);
                
                current_task_info = sprintf('执行中: %s臂 → %s', upper(task.arm), lego_blocks.ids(task.block_id));
                
                progress = min(1, (current_time - task.start_time) / task.duration);
                
                % Pass the specific block's size to the trajectory calculation
                block_size = lego_blocks.sizes(task.block_id, :);
                [current_pos, T_gripper] = calculate_trajectory(task.pick_pos, task.place_pos, progress, task.arm, block_size);

                lego_blocks.positions(task.block_id, :, step) = current_pos;
                lego_blocks.states(task.block_id, step) = "moving";

                if strcmp(task.arm, 'left')
                    [q_sol, solInfo] = ikLeft('gripper_l_base', T_gripper, weights, q_current');
                    if strcmp(solInfo.Status, 'success')
                        q_current(1:7) = q_sol(1:7);
                    end
                else
                    [q_sol, solInfo] = ikRight('gripper_r_base', T_gripper, weights, q_current');
                    if strcmp(solInfo.Status, 'success')
                        q_current(8:14) = q_sol(8:14);
                    end
                end
                break; % Process only one active task per time step
            end
        end
        
        % 更新所有积木位置 (处理非活动积木)
        for i = 1:lego_blocks.count
             if lego_blocks.states(i, step-1) == "free"
                 lego_blocks.positions(i, :, step) = lego_blocks.positions(i, :, step-1);
                 lego_blocks.states(i, step) = "free";
             elseif lego_blocks.states(i, step-1) == "placed"
                 task_idx = find([task_sequence.tasks.block_id] == i);
                 lego_blocks.positions(i, :, step) = task_sequence.tasks(task_idx).place_pos;
                 lego_blocks.states(i, step) = "placed";
             end
        end
        
        % 动画帧更新
        if current_time - last_frame_time >= frame_interval
            % 更新机器人姿态
            show(yumi, q_current', 'Visuals', 'on', 'Collisions', 'off', 'PreservePlot', false);
            ax.View = [45, 30]; % 保持视角
            
            % 更新积木
            for i = 1:lego_blocks.count
                pos = lego_blocks.positions(i, :, step);
                [vertices, ~] = create_lego_block_mesh(lego_blocks.sizes(i, :)); % Use individual size
                set(block_handles(i), 'Vertices', vertices + pos);
                set(text_handles(i), 'Position', pos + [0, 0, lego_blocks.sizes(i, 3) + 0.01]);
            end
            
            % 更新状态文本
            if active_task_found
                set(status_text, 'String', sprintf('时间: %.1fs | %s', current_time, current_task_info));
            else
                set(status_text, 'String', sprintf('时间: %.1fs | 等待任务...', current_time));
            end
            
            completed_count = sum(arrayfun(@(x) strcmp(x.status, 'completed'), task_sequence.tasks));
            title(sprintf('YuMi LEGO 动画 | 已完成: %d/%d', completed_count, lego_blocks.count));
            
            drawnow;
            
            frame = getframe(fig);
            writeVideo(video_writer, frame);
            
            frame_count = frame_count + 1;
            last_frame_time = current_time;
        end
    end
    
    close(video_writer);
    fprintf('\n✓ 动画录制完成: %s\n', video_filename);
    
    sim_results.time = time_steps;
    sim_results.lego_blocks = lego_blocks;
    save('enhanced_animation_results.mat', 'sim_results');
    saveas(fig, 'enhanced_animation_final.png');
    
    fprintf('=== 仿真完成 ===\n');
end

function [vertices, faces] = create_lego_block_mesh(size)
    l = size(1) / 2; w = size(2) / 2; h = size(3);
    vertices = [-l -w 0; l -w 0; l w 0; -l w 0; -l -w h; l -w h; l w h; -l w h];
    faces = [1 2 3 4; 5 8 7 6; 1 5 6 2; 3 7 8 4; 1 4 8 5; 2 6 7 3];
end

function [current_pos, T_gripper] = calculate_trajectory(start_pos, end_pos, progress, arm, block_size)
    % Recalculated trajectory based on block_size
    hover_clearance = 0.1; % Safe Z for horizontal moves
    
    % Define trajectory stages
    t_descend_grasp = 0.25;
    t_grasp_lift = 0.4;
    t_move_horizontal = 0.85;
    t_descend_place = 1.0;

    gripper_pos = [0,0,0];
    block_is_attached = false;

    if progress <= t_descend_grasp
        % 1. Descend to grasp
        p = progress / t_descend_grasp;
        start_hover = start_pos + [0, 0, hover_clearance];
        gripper_pos = start_hover * (1-p) + start_pos * p;
    elseif progress <= t_grasp_lift
        % 2. Grasp and lift vertically
        block_is_attached = true;
        p = (progress - t_descend_grasp) / (t_grasp_lift - t_descend_grasp);
        end_lift = start_pos + [0, 0, hover_clearance];
        gripper_pos = start_pos * (1-p) + end_lift * p;
    elseif progress <= t_move_horizontal
        % 3. Move horizontally at safe height
        block_is_attached = true;
        p = (progress - t_grasp_lift) / (t_move_horizontal - t_grasp_lift);
        start_travel = start_pos + [0, 0, hover_clearance];
        end_travel = end_pos + [0, 0, hover_clearance];
        gripper_pos = start_travel * (1-p) + end_travel * p;
    else
        % 4. Descend to place
        block_is_attached = true;
        p = (progress - t_move_horizontal) / (t_descend_place - t_move_horizontal);
        start_drop = end_pos + [0, 0, hover_clearance];
        gripper_pos = start_drop * (1-p) + end_pos * p;
    end
    
    if block_is_attached
        current_pos = gripper_pos;
    else
        current_pos = start_pos;
    end
    
    T_gripper = trvec2tform(gripper_pos + [0, 0, block_size(3)/2]);
    R_gripper = [1 0 0; 0 -1 0; 0 0 -1];
    T_gripper(1:3, 1:3) = R_gripper;
end

function size_vec = parse_brick_type(type_str)
    persistent stud_pitch brick_height;
    if isempty(stud_pitch)
        stud_pitch = 0.008; % 8mm
        brick_height = 0.0096; % 9.6mm
    end
    
    dims = sscanf(lower(type_str), '%dx%d');
    if length(dims) == 2
        width = dims(1) * stud_pitch - 0.0002;
        depth = dims(2) * stud_pitch - 0.0002;
        
        if contains(type_str, 'Vert', 'IgnoreCase', true)
            size_vec = [depth, width, brick_height];
        else
            size_vec = [width, depth, brick_height];
        end
    else
        size_vec = [0.0318, 0.0158, 0.0096]; % Default
    end
end
