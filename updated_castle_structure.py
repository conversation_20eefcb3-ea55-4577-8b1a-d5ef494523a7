#!/usr/bin/env python3
"""
更新的城堡结构定义
基于Excel坐标数据自动生成

作者: AI Assistant (基于坐标数据)
日期: 2025-01-27
版本: 2.0
"""

import numpy as np
from typing import List, Dict, Tuple

class UpdatedCastleStructure:
    """更新的城堡结构定义"""

    def __init__(self):
        """初始化城堡结构"""
        self.layers = self._define_layers()
        self.total_layers = len(self.layers)

    def _define_layers(self) -> Dict[int, List[Dict]]:
        """定义所有层级的积木"""
        layers = {}

        # 第1层 - 12个积木
        layers[1] = [
            {
                'id': 'B02',
                'type': '2x4 (Vert)',
                'position': (0.50000, -0.08745, 0.00480),
                'size': (2, 4),
                'orientation': 'vertical',
                'level': 1
            },
            {
                'id': 'B04',
                'type': '2x4 (<PERSON><PERSON>)',
                'position': (0.49205, -0.06360, 0.00480),
                'size': (4, 2),
                'orientation': 'horizontal',
                'level': 1
            },
            {
                'id': 'B06',
                'type': '2x4 (Horz)',
                'position': (0.50795, -0.06360, 0.00480),
                'size': (4, 2),
                'orientation': 'horizontal',
                'level': 1
            },
            {
                'id': 'B08',
                'type': '2x4 (Horz)',
                'position': (0.49205, -0.03180, 0.00480),
                'size': (4, 2),
                'orientation': 'horizontal',
                'level': 1
            },
            {
                'id': 'B10',
                'type': '2x4 (Horz)',
                'position': (0.50795, -0.03180, 0.00480),
                'size': (4, 2),
                'orientation': 'horizontal',
                'level': 1
            },
            {
                'id': 'B11',
                'type': '2x4 (Horz)',
                'position': (0.49205, 0.00000, 0.00480),
                'size': (4, 2),
                'orientation': 'horizontal',
                'level': 1
            },
            {
                'id': 'B12',
                'type': '2x4 (Horz)',
                'position': (0.50795, 0.00000, 0.00480),
                'size': (4, 2),
                'orientation': 'horizontal',
                'level': 1
            },
            {
                'id': 'B07',
                'type': '2x4 (Horz)',
                'position': (0.49205, 0.03180, 0.00480),
                'size': (4, 2),
                'orientation': 'horizontal',
                'level': 1
            },
            {
                'id': 'B09',
                'type': '2x4 (Horz)',
                'position': (0.50795, 0.03180, 0.00480),
                'size': (4, 2),
                'orientation': 'horizontal',
                'level': 1
            },
            {
                'id': 'B03',
                'type': '2x4 (Horz)',
                'position': (0.49205, 0.06360, 0.00480),
                'size': (4, 2),
                'orientation': 'horizontal',
                'level': 1
            },
            {
                'id': 'B05',
                'type': '2x4 (Horz)',
                'position': (0.50795, 0.06360, 0.00480),
                'size': (4, 2),
                'orientation': 'horizontal',
                'level': 1
            },
            {
                'id': 'B01',
                'type': '2x4 (Vert)',
                'position': (0.50000, 0.08745, 0.00480),
                'size': (2, 4),
                'orientation': 'vertical',
                'level': 1
            },
        ]

        # 第2层 - 12个积木
        layers[2] = [
            {
                'id': 'B14',
                'type': '2x4 (Horz)',
                'position': (0.49205, -0.07950, 0.01440),
                'size': (4, 2),
                'orientation': 'horizontal',
                'level': 2
            },
            {
                'id': 'B16',
                'type': '2x4 (Horz)',
                'position': (0.50795, -0.07950, 0.01440),
                'size': (4, 2),
                'orientation': 'horizontal',
                'level': 2
            },
            {
                'id': 'B18',
                'type': '2x4 (Horz)',
                'position': (0.49205, -0.04770, 0.01440),
                'size': (4, 2),
                'orientation': 'horizontal',
                'level': 2
            },
            {
                'id': 'B20',
                'type': '2x4 (Horz)',
                'position': (0.50795, -0.04770, 0.01440),
                'size': (4, 2),
                'orientation': 'horizontal',
                'level': 2
            },
            {
                'id': 'B22',
                'type': '2x4 (Horz)',
                'position': (0.49205, -0.01590, 0.01440),
                'size': (4, 2),
                'orientation': 'horizontal',
                'level': 2
            },
            {
                'id': 'B24',
                'type': '2x4 (Horz)',
                'position': (0.50795, -0.01590, 0.01440),
                'size': (4, 2),
                'orientation': 'horizontal',
                'level': 2
            },
            {
                'id': 'B21',
                'type': '2x4 (Horz)',
                'position': (0.49205, 0.01590, 0.01440),
                'size': (4, 2),
                'orientation': 'horizontal',
                'level': 2
            },
            {
                'id': 'B23',
                'type': '2x4 (Horz)',
                'position': (0.50795, 0.01590, 0.01440),
                'size': (4, 2),
                'orientation': 'horizontal',
                'level': 2
            },
            {
                'id': 'B17',
                'type': '2x4 (Horz)',
                'position': (0.49205, 0.04770, 0.01440),
                'size': (4, 2),
                'orientation': 'horizontal',
                'level': 2
            },
            {
                'id': 'B19',
                'type': '2x4 (Horz)',
                'position': (0.50795, 0.04770, 0.01440),
                'size': (4, 2),
                'orientation': 'horizontal',
                'level': 2
            },
            {
                'id': 'B13',
                'type': '2x4 (Horz)',
                'position': (0.49205, 0.07950, 0.01440),
                'size': (4, 2),
                'orientation': 'horizontal',
                'level': 2
            },
            {
                'id': 'B15',
                'type': '2x4 (Horz)',
                'position': (0.50795, 0.07950, 0.01440),
                'size': (4, 2),
                'orientation': 'horizontal',
                'level': 2
            },
        ]

        # 第3层 - 7个积木
        layers[3] = [
            {
                'id': 'B26',
                'type': '2x4 (Vert)',
                'position': (0.50000, -0.08745, 0.02400),
                'size': (2, 4),
                'orientation': 'vertical',
                'level': 3
            },
            {
                'id': 'B28',
                'type': '2x4 (Vert)',
                'position': (0.50000, -0.07155, 0.02400),
                'size': (2, 4),
                'orientation': 'vertical',
                'level': 3
            },
            {
                'id': 'B30',
                'type': '2x4 (Vert)',
                'position': (0.50000, -0.01590, 0.02400),
                'size': (2, 4),
                'orientation': 'vertical',
                'level': 3
            },
            {
                'id': 'B31',
                'type': '2x4 (Vert)',
                'position': (0.50000, 0.00000, 0.02400),
                'size': (2, 4),
                'orientation': 'vertical',
                'level': 3
            },
            {
                'id': 'B29',
                'type': '2x4 (Vert)',
                'position': (0.50000, 0.01590, 0.02400),
                'size': (2, 4),
                'orientation': 'vertical',
                'level': 3
            },
            {
                'id': 'B27',
                'type': '2x4 (Vert)',
                'position': (0.50000, 0.07155, 0.02400),
                'size': (2, 4),
                'orientation': 'vertical',
                'level': 3
            },
            {
                'id': 'B25',
                'type': '2x4 (Vert)',
                'position': (0.50000, 0.08745, 0.02400),
                'size': (2, 4),
                'orientation': 'vertical',
                'level': 3
            },
        ]

        # 第4层 - 8个积木
        layers[4] = [
            {
                'id': 'S02',
                'type': 'Slope Brick 45 2×4 Double Inv',
                'position': (0.50000, -0.08745, 0.03360),
                'size': (4, 2),
                'orientation': 'horizontal',
                'level': 4
            },
            {
                'id': 'S04',
                'type': 'Slope Brick 45 2×4 Double Inv',
                'position': (0.50000, -0.07155, 0.03360),
                'size': (4, 2),
                'orientation': 'horizontal',
                'level': 4
            },
            {
                'id': 'S06',
                'type': 'Slope Brick 33 3×2',
                'position': (0.49205, -0.01590, 0.03360),
                'size': (3, 2),
                'orientation': 'horizontal',
                'level': 4
            },
            {
                'id': 'S08',
                'type': 'SlopeBrick 33 3×2',
                'position': (0.50795, -0.01590, 0.03360),
                'size': (3, 2),
                'orientation': 'horizontal',
                'level': 4
            },
            {
                'id': 'S05',
                'type': 'Slope Brick 33 3×2',
                'position': (0.49205, 0.01590, 0.03360),
                'size': (3, 2),
                'orientation': 'horizontal',
                'level': 4
            },
            {
                'id': 'S03',
                'type': 'Slope Brick 45 2×4 Double Inv',
                'position': (0.50000, 0.07155, 0.03360),
                'size': (4, 2),
                'orientation': 'horizontal',
                'level': 4
            },
            {
                'id': 'S01',
                'type': 'Slope Brick 45 2×4 Double Inv',
                'position': (0.50000, 0.08745, 0.03360),
                'size': (4, 2),
                'orientation': 'horizontal',
                'level': 4
            },
            {
                'id': 'S07',
                'type': 'Slope Brick 33 3×2',
                'position': (0.50795, 0.51590, 0.03360),
                'size': (3, 2),
                'orientation': 'horizontal',
                'level': 4
            },
        ]

        # 第5层 - 1个积木
        layers[5] = [
            {
                'id': 'B33',
                'type': '2x4 (Vert)',
                'position': (0.50000, 0.00000, 0.04320),
                'size': (2, 4),
                'orientation': 'vertical',
                'level': 5
            },
        ]

        # 第6层 - 1个积木
        layers[6] = [
            {
                'id': 'B32',
                'type': '2x4 (Vert)',
                'position': (0.50000, 0.00000, 0.05280),
                'size': (2, 4),
                'orientation': 'vertical',
                'level': 6
            },
        ]

        # 第7层 - 2个积木
        layers[7] = [
            {
                'id': 'B35',
                'type': '2x4 (Horz)',
                'position': (0.49205, 0.00000, 0.06240),
                'size': (4, 2),
                'orientation': 'horizontal',
                'level': 7
            },
            {
                'id': 'B34',
                'type': '2x4 (Horz)',
                'position': (0.50795, 0.00000, 0.06240),
                'size': (4, 2),
                'orientation': 'horizontal',
                'level': 7
            },
        ]

        # 第8层 - 4个积木
        layers[8] = [
            {
                'id': 'C02',
                'type': 'Cone 2x2',
                'position': (0.49205, 0.49205, 0.07680),
                'size': (2, 2),
                'orientation': 'horizontal',
                'level': 8
            },
            {
                'id': 'C04',
                'type': 'Cone 2x2',
                'position': (0.50795, 0.49205, 0.07680),
                'size': (2, 2),
                'orientation': 'horizontal',
                'level': 8
            },
            {
                'id': 'C01',
                'type': 'Cone 2x2',
                'position': (0.49205, 0.50795, 0.07680),
                'size': (2, 2),
                'orientation': 'horizontal',
                'level': 8
            },
            {
                'id': 'C03',
                'type': 'Cone 2x2',
                'position': (0.50795, 0.50795, 0.07680),
                'size': (2, 2),
                'orientation': 'horizontal',
                'level': 8
            },
        ]

        return layers

    def get_layer(self, level: int) -> List[Dict]:
        """获取指定层级的积木"""
        return self.layers.get(level, [])

    def get_all_bricks(self) -> List[Dict]:
        """获取所有积木"""
        all_bricks = []
        for level in sorted(self.layers.keys()):
            all_bricks.extend(self.layers[level])
        return all_bricks

    def get_layer_count(self) -> int:
        """获取层数"""
        return self.total_layers

    def print_structure_summary(self):
        """打印结构摘要"""
        print(f'城堡结构摘要:')
        print(f'  总层数: {self.total_layers}')
        total_bricks = 0
        for level in sorted(self.layers.keys()):
            brick_count = len(self.layers[level])
            total_bricks += brick_count
            print(f'  第{level}层: {brick_count}个积木')
        print(f'  总积木数: {total_bricks}')

# 创建城堡结构实例
castle_structure = UpdatedCastleStructure()

if __name__ == '__main__':
    castle_structure.print_structure_summary()