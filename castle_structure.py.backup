#!/usr/bin/env python3
"""
LEGO城堡结构定义模块
根据参考图片精确定义8层城堡的每个积木位置和朝向

作者: AI Assistant
日期: 2025-01-26
版本: 1.0
"""

import numpy as np
from typing import List, Dict, <PERSON><PERSON>
from dataclasses import dataclass
from enum import Enum
import json

class BrickType(Enum):
    """积木类型枚举"""
    BRICK_2X4 = "brick_2x4"      # 标准2x4积木
    BRICK_2X2 = "brick_2x2"      # 2x2积木
    BRICK_1X4 = "brick_1x4"      # 1x4积木
    BRICK_1X2 = "brick_1x2"      # 1x2积木
    SLOPE_2X4 = "slope_2x4"      # 2x4斜坡
    CONE = "cone"                # 圆锥塔顶

class BrickOrientation(Enum):
    """积木朝向枚举"""
    HORIZONTAL = 0      # 水平放置 (0度)
    VERTICAL = 90       # 垂直放置 (90度)
    ROTATED_180 = 180   # 180度旋转
    ROTATED_270 = 270   # 270度旋转

@dataclass
class BrickInfo:
    """积木信息数据类"""
    id: str
    type: BrickType
    position: Tuple[float, float, float]  # (x, y, z) 位置
    orientation: BrickOrientation         # 朝向
    level: int                           # 所属层级
    color: str = "tan"                   # 颜色
    dependencies: List[str] = None       # 依赖的积木ID

    def __post_init__(self):
        if self.dependencies is None:
            self.dependencies = []

class CastleStructureDefinition:
    """城堡结构定义类"""
    
    def __init__(self):
        """初始化城堡结构定义"""
        # 基础参数 (基于真实LEGO 2x4积木尺寸)
        self.brick_length = 0.0318  # 31.8mm
        self.brick_width = 0.0159   # 15.9mm
        self.brick_height = 0.0096  # 9.6mm
        
        # 城堡基础位置 (工作台中心)
        self.castle_center = [0.5, 0.0, 0.06]  # (x, y, z)
        
        # 生成完整的城堡结构
        self.castle_bricks = self._generate_castle_structure()
        
        print(f"城堡结构定义完成 - 总积木数: {len(self.castle_bricks)}")
    
    def _generate_castle_structure(self) -> List[BrickInfo]:
        """生成完整的城堡结构"""
        all_bricks = []
        
        # Level 1: 基础层
        all_bricks.extend(self._generate_level_1())
        
        # Level 2: 左侧建筑第一层
        all_bricks.extend(self._generate_level_2())
        
        # Level 3: 左侧建筑第二层和屋顶
        all_bricks.extend(self._generate_level_3())
        
        # Level 4: 右侧建筑屋顶
        all_bricks.extend(self._generate_level_4())
        
        # Level 5: 中央塔楼第一层
        all_bricks.extend(self._generate_level_5())
        
        # Level 6: 中央塔楼第二层
        all_bricks.extend(self._generate_level_6())
        
        # Level 7: 中央塔楼第三层
        all_bricks.extend(self._generate_level_7())
        
        # Level 8: 塔顶圆锥
        all_bricks.extend(self._generate_level_8())
        
        return all_bricks
    
    def _generate_level_1(self) -> List[BrickInfo]:
        """生成Level 1 - 基础层"""
        bricks = []
        base_z = self.castle_center[2]
        
        # 基础平台 - 大矩形基座
        # 前排积木 (6个2x4积木)
        for i in range(6):
            x = self.castle_center[0] - 0.095 + i * self.brick_length
            y = self.castle_center[1] - 0.032
            
            brick = BrickInfo(
                id=f"L1_base_front_{i+1}",
                type=BrickType.BRICK_2X4,
                position=(x, y, base_z),
                orientation=BrickOrientation.HORIZONTAL,
                level=1,
                color="tan"
            )
            bricks.append(brick)
        
        # 后排积木 (6个2x4积木)
        for i in range(6):
            x = self.castle_center[0] - 0.095 + i * self.brick_length
            y = self.castle_center[1] + 0.032
            
            brick = BrickInfo(
                id=f"L1_base_back_{i+1}",
                type=BrickType.BRICK_2X4,
                position=(x, y, base_z),
                orientation=BrickOrientation.HORIZONTAL,
                level=1,
                color="tan"
            )
            bricks.append(brick)
        
        # 中间填充积木 (4个2x4积木)
        for i in range(4):
            x = self.castle_center[0] - 0.063 + i * self.brick_length
            y = self.castle_center[1]
            
            brick = BrickInfo(
                id=f"L1_base_middle_{i+1}",
                type=BrickType.BRICK_2X4,
                position=(x, y, base_z),
                orientation=BrickOrientation.HORIZONTAL,
                level=1,
                color="tan"
            )
            bricks.append(brick)
        
        return bricks
    
    def _generate_level_2(self) -> List[BrickInfo]:
        """生成Level 2 - 左侧建筑第一层"""
        bricks = []
        base_z = self.castle_center[2] + self.brick_height
        
        # 左侧建筑主体 (3个2x4积木)
        positions = [
            (-0.08, -0.016, base_z),   # 左前
            (-0.08, 0.016, base_z),    # 左后
            (-0.048, 0.0, base_z)      # 左中
        ]
        
        for i, pos in enumerate(positions):
            brick = BrickInfo(
                id=f"L2_left_building_{i+1}",
                type=BrickType.BRICK_2X4,
                position=pos,
                orientation=BrickOrientation.HORIZONTAL,
                level=2,
                color="tan",
                dependencies=[f"L1_base_front_{i+1}", f"L1_base_back_{i+1}"]
            )
            bricks.append(brick)
        
        return bricks
    
    def _generate_level_3(self) -> List[BrickInfo]:
        """生成Level 3 - 左侧建筑第二层和屋顶"""
        bricks = []
        base_z = self.castle_center[2] + 2 * self.brick_height
        
        # 左侧建筑第二层 (2个2x4积木)
        positions = [
            (-0.08, -0.016, base_z),   # 左前
            (-0.08, 0.016, base_z)     # 左后
        ]
        
        for i, pos in enumerate(positions):
            brick = BrickInfo(
                id=f"L3_left_wall_{i+1}",
                type=BrickType.BRICK_2X4,
                position=pos,
                orientation=BrickOrientation.HORIZONTAL,
                level=3,
                color="tan",
                dependencies=[f"L2_left_building_{i+1}"]
            )
            bricks.append(brick)
        
        # 屋顶斜坡 (2个斜坡积木)
        roof_z = base_z + self.brick_height * 0.5
        roof_positions = [
            (-0.08, -0.008, roof_z),   # 左屋顶前
            (-0.08, 0.008, roof_z)     # 左屋顶后
        ]
        
        for i, pos in enumerate(roof_positions):
            brick = BrickInfo(
                id=f"L3_left_roof_{i+1}",
                type=BrickType.SLOPE_2X4,
                position=pos,
                orientation=BrickOrientation.HORIZONTAL,
                level=3,
                color="dark_gray",
                dependencies=[f"L3_left_wall_{i+1}"]
            )
            bricks.append(brick)
        
        return bricks
    
    def _generate_level_4(self) -> List[BrickInfo]:
        """生成Level 4 - 右侧建筑屋顶"""
        bricks = []
        base_z = self.castle_center[2] + 3 * self.brick_height
        
        # 右侧建筑墙体 (2个2x4积木)
        positions = [
            (0.08, -0.016, base_z - self.brick_height),   # 右前墙
            (0.08, 0.016, base_z - self.brick_height)     # 右后墙
        ]
        
        for i, pos in enumerate(positions):
            brick = BrickInfo(
                id=f"L4_right_wall_{i+1}",
                type=BrickType.BRICK_2X4,
                position=pos,
                orientation=BrickOrientation.HORIZONTAL,
                level=4,
                color="tan"
            )
            bricks.append(brick)
        
        # 右侧屋顶斜坡 (2个斜坡积木)
        roof_positions = [
            (0.08, -0.008, base_z),    # 右屋顶前
            (0.08, 0.008, base_z)      # 右屋顶后
        ]
        
        for i, pos in enumerate(roof_positions):
            brick = BrickInfo(
                id=f"L4_right_roof_{i+1}",
                type=BrickType.SLOPE_2X4,
                position=pos,
                orientation=BrickOrientation.HORIZONTAL,
                level=4,
                color="dark_gray",
                dependencies=[f"L4_right_wall_{i+1}"]
            )
            bricks.append(brick)
        
        return bricks
    
    def _generate_level_5(self) -> List[BrickInfo]:
        """生成Level 5 - 中央塔楼第一层"""
        bricks = []
        base_z = self.castle_center[2] + 4 * self.brick_height
        
        # 中央塔楼基座 (4个2x4积木形成方形)
        positions = [
            (0.016, -0.016, base_z),   # 前左
            (0.016, 0.016, base_z),    # 后左
            (-0.016, -0.016, base_z),  # 前右
            (-0.016, 0.016, base_z)    # 后右
        ]
        
        for i, pos in enumerate(positions):
            brick = BrickInfo(
                id=f"L5_tower_base_{i+1}",
                type=BrickType.BRICK_2X4,
                position=pos,
                orientation=BrickOrientation.HORIZONTAL,
                level=5,
                color="tan"
            )
            bricks.append(brick)
        
        return bricks
    
    def _generate_level_6(self) -> List[BrickInfo]:
        """生成Level 6 - 中央塔楼第二层"""
        bricks = []
        base_z = self.castle_center[2] + 5 * self.brick_height
        
        # 塔楼第二层 (4个2x4积木)
        positions = [
            (0.016, -0.016, base_z),   # 前左
            (0.016, 0.016, base_z),    # 后左
            (-0.016, -0.016, base_z),  # 前右
            (-0.016, 0.016, base_z)    # 后右
        ]
        
        for i, pos in enumerate(positions):
            brick = BrickInfo(
                id=f"L6_tower_mid_{i+1}",
                type=BrickType.BRICK_2X4,
                position=pos,
                orientation=BrickOrientation.HORIZONTAL,
                level=6,
                color="tan",
                dependencies=[f"L5_tower_base_{i+1}"]
            )
            bricks.append(brick)
        
        return bricks
    
    def _generate_level_7(self) -> List[BrickInfo]:
        """生成Level 7 - 中央塔楼第三层"""
        bricks = []
        base_z = self.castle_center[2] + 6 * self.brick_height
        
        # 塔楼第三层 (4个2x2积木，为塔顶做准备)
        positions = [
            (0.008, -0.008, base_z),   # 前左
            (0.008, 0.008, base_z),    # 后左
            (-0.008, -0.008, base_z),  # 前右
            (-0.008, 0.008, base_z)    # 后右
        ]
        
        for i, pos in enumerate(positions):
            brick = BrickInfo(
                id=f"L7_tower_top_{i+1}",
                type=BrickType.BRICK_2X2,
                position=pos,
                orientation=BrickOrientation.HORIZONTAL,
                level=7,
                color="tan",
                dependencies=[f"L6_tower_mid_{i+1}"]
            )
            bricks.append(brick)
        
        return bricks
    
    def _generate_level_8(self) -> List[BrickInfo]:
        """生成Level 8 - 塔顶圆锥"""
        bricks = []
        base_z = self.castle_center[2] + 7 * self.brick_height
        
        # 四个圆锥塔顶
        positions = [
            (0.008, -0.008, base_z),   # 前左圆锥
            (0.008, 0.008, base_z),    # 后左圆锥
            (-0.008, -0.008, base_z),  # 前右圆锥
            (-0.008, 0.008, base_z)    # 后右圆锥
        ]
        
        for i, pos in enumerate(positions):
            brick = BrickInfo(
                id=f"L8_cone_{i+1}",
                type=BrickType.CONE,
                position=pos,
                orientation=BrickOrientation.HORIZONTAL,
                level=8,
                color="dark_gray",
                dependencies=[f"L7_tower_top_{i+1}"]
            )
            bricks.append(brick)
        
        return bricks
    
    def get_level_bricks(self, level: int) -> List[Dict]:
        """获取指定层级的积木"""
        level_bricks = [brick for brick in self.castle_bricks if brick.level == level]
        
        # 转换为字典格式
        return [
            {
                'id': brick.id,
                'type': brick.type.value,
                'position': list(brick.position),
                'orientation': brick.orientation.value,
                'level': brick.level,
                'color': brick.color,
                'dependencies': brick.dependencies
            }
            for brick in level_bricks
        ]
    
    def get_total_brick_count(self) -> int:
        """获取总积木数量"""
        return len(self.castle_bricks)
    
    def get_brick_by_id(self, brick_id: str) -> BrickInfo:
        """根据ID获取积木信息"""
        for brick in self.castle_bricks:
            if brick.id == brick_id:
                return brick
        return None
    
    def get_level_count(self) -> int:
        """获取总层数"""
        return max(brick.level for brick in self.castle_bricks)
    
    def export_structure(self, filename: str):
        """导出城堡结构到JSON文件"""
        structure_data = {
            'castle_info': {
                'total_bricks': len(self.castle_bricks),
                'total_levels': self.get_level_count(),
                'castle_center': self.castle_center,
                'brick_dimensions': {
                    'length': self.brick_length,
                    'width': self.brick_width,
                    'height': self.brick_height
                }
            },
            'bricks': [
                {
                    'id': brick.id,
                    'type': brick.type.value,
                    'position': list(brick.position),
                    'orientation': brick.orientation.value,
                    'level': brick.level,
                    'color': brick.color,
                    'dependencies': brick.dependencies
                }
                for brick in self.castle_bricks
            ]
        }
        
        with open(filename, 'w') as f:
            json.dump(structure_data, f, indent=2)
        
        print(f"城堡结构已导出到: {filename}")
    
    def validate_structure(self) -> bool:
        """验证城堡结构的完整性"""
        # 检查依赖关系
        all_ids = {brick.id for brick in self.castle_bricks}
        
        for brick in self.castle_bricks:
            for dep_id in brick.dependencies:
                if dep_id not in all_ids:
                    print(f"警告: 积木 {brick.id} 依赖的 {dep_id} 不存在")
                    return False
        
        # 检查层级连续性
        levels = sorted(set(brick.level for brick in self.castle_bricks))
        expected_levels = list(range(1, max(levels) + 1))
        
        if levels != expected_levels:
            print(f"警告: 层级不连续 - 期望: {expected_levels}, 实际: {levels}")
            return False
        
        print("✅ 城堡结构验证通过")
        return True

def main():
    """测试城堡结构定义"""
    print("🏰 LEGO城堡结构定义测试")
    print("=" * 40)
    
    # 创建城堡结构
    castle = CastleStructureDefinition()
    
    # 验证结构
    castle.validate_structure()
    
    # 导出结构
    castle.export_structure('castle_structure.json')
    
    # 显示统计信息
    print(f"\n📊 城堡统计信息:")
    print(f"总积木数: {castle.get_total_brick_count()}")
    print(f"总层数: {castle.get_level_count()}")
    
    for level in range(1, castle.get_level_count() + 1):
        level_bricks = castle.get_level_bricks(level)
        print(f"Level {level}: {len(level_bricks)} 个积木")
    
    print("\n✅ 城堡结构定义完成")

if __name__ == "__main__":
    main()
