function [sim_results] = simplified_physics_simulation()
    % Simplified Physics Simulation for YuMi LEGO Stacking
    % This replaces Simscape Multibody with pure MATLAB implementation
    
    fprintf('=== 简化物理仿真系统 ===\n\n');
    
    %% 1. Initialize simulation parameters
    fprintf('1. 初始化仿真参数...\n');
    
    % Time parameters
    dt = 0.01;  % Time step (10ms)
    t_total = 30;  % Total simulation time (30 seconds)
    time_steps = 0:dt:t_total;
    n_steps = length(time_steps);
    
    % Physics parameters
    gravity = [0, 0, -9.81];  % Gravity vector
    friction_coeff = 0.7;     % Friction coefficient
    restitution = 0.3;        % Collision restitution
    
    fprintf('✓ 仿真参数设置完成\n');
    fprintf('  - 时间步长: %.3f s\n', dt);
    fprintf('  - 总仿真时间: %.1f s\n', t_total);
    fprintf('  - 重力加速度: [%.1f, %.1f, %.2f] m/s²\n', gravity);
    
    %% 2. Load robot and environment
    fprintf('\n2. 加载机器人和环境...\n');
    
    try
        [yumi, qHome, table, ax] = setupRobotEnv_fixed();
        fprintf('✓ 机器人环境加载成功\n');
    catch ME
        fprintf('✗ 机器人环境加载失败: %s\n', ME.message);
        return;
    end
    
    %% 3. Initialize LEGO blocks
    fprintf('\n3. 初始化 LEGO 积木...\n');
    
    % Load LEGO configuration
    lego_config;
    
    % Create LEGO block structure
    lego_blocks = struct();
    lego_blocks.count = 12;  % First layer has 12 blocks
    lego_blocks.size = [0.0318, 0.0159, 0.0200];  % LEGO dimensions (m)
    lego_blocks.mass = 0.002;  % LEGO mass (2g)
    
    % Initialize positions (starting positions for each block)
    lego_blocks.positions = zeros(lego_blocks.count, 3, n_steps);
    lego_blocks.velocities = zeros(lego_blocks.count, 3, n_steps);
    lego_blocks.orientations = zeros(lego_blocks.count, 3, n_steps);  % Euler angles
    lego_blocks.states = strings(lego_blocks.count, n_steps);  % 'free', 'grasped', 'placed'
    
    % Set initial positions (left and right storage areas)
    for i = 1:6  % Left arm blocks
        lego_blocks.positions(i, :, 1) = [0.2, -0.1 + (i-1)*0.04, 0.07];
        lego_blocks.states(i, 1) = "free";
    end
    
    for i = 7:12  % Right arm blocks
        lego_blocks.positions(i, :, 1) = [0.8, -0.1 + (i-7)*0.04, 0.07];
        lego_blocks.states(i, 1) = "free";
    end
    
    fprintf('✓ LEGO 积木初始化完成\n');
    fprintf('  - 积木数量: %d\n', lego_blocks.count);
    fprintf('  - 积木尺寸: [%.4f, %.4f, %.4f] m\n', lego_blocks.size);
    fprintf('  - 积木质量: %.3f kg\n', lego_blocks.mass);
    
    %% 4. Initialize robot states
    fprintf('\n4. 初始化机器人状态...\n');
    
    % Robot joint states
    robot_states = struct();
    robot_states.q_left = zeros(7, n_steps);   % Left arm joint angles
    robot_states.q_right = zeros(7, n_steps);  % Right arm joint angles
    robot_states.qd_left = zeros(7, n_steps);  % Left arm joint velocities
    robot_states.qd_right = zeros(7, n_steps); % Right arm joint velocities
    robot_states.gripper_left = zeros(1, n_steps);   % Left gripper state (0-1)
    robot_states.gripper_right = zeros(1, n_steps);  % Right gripper state (0-1)
    
    % Set initial configuration
    robot_states.q_left(:, 1) = qHome(1:7)';
    robot_states.q_right(:, 1) = qHome(8:14)';
    robot_states.gripper_left(1) = 0;  % Open
    robot_states.gripper_right(1) = 0; % Open
    
    % End-effector positions
    robot_states.ee_pos_left = zeros(3, n_steps);
    robot_states.ee_pos_right = zeros(3, n_steps);
    
    % Calculate initial end-effector positions
    T_left = getTransform(yumi, qHome, 'gripper_l_base');
    T_right = getTransform(yumi, qHome, 'gripper_r_base');
    robot_states.ee_pos_left(:, 1) = T_left(1:3, 4);
    robot_states.ee_pos_right(:, 1) = T_right(1:3, 4);
    
    fprintf('✓ 机器人状态初始化完成\n');
    fprintf('  - 左臂初始位置: [%.3f, %.3f, %.3f]\n', robot_states.ee_pos_left(:, 1));
    fprintf('  - 右臂初始位置: [%.3f, %.3f, %.3f]\n', robot_states.ee_pos_right(:, 1));
    
    %% 5. Define target positions for first layer
    fprintf('\n5. 定义第一层目标位置...\n');
    
    % Target positions from LEGO configuration
    target_positions = [
        0.4125, 0.0000, 0.0648;  % B01 -> Target01
        0.5875, 0.0000, 0.0648;  % B02 -> Target12
        0.4364, -0.0080, 0.0648; % B03 -> Target02
        0.5636, -0.0080, 0.0648; % B04 -> Target10
        0.4364, 0.0080, 0.0648;  % B05 -> Target03
        0.5636, 0.0080, 0.0648;  % B06 -> Target11
        0.4682, -0.0080, 0.0648; % B07 -> Target04
        0.5318, -0.0080, 0.0648; % B08 -> Target08
        0.4682, 0.0080, 0.0648;  % B09 -> Target05
        0.5318, 0.0080, 0.0648;  % B10 -> Target09
        0.5000, -0.0080, 0.0648; % B11 -> Target06
        0.5000, 0.0080, 0.0648   % B12 -> Target07
    ];
    
    fprintf('✓ 目标位置定义完成\n');
    fprintf('  - 目标位置数量: %d\n', size(target_positions, 1));
    
    %% 6. Create simple task sequence
    fprintf('\n6. 创建任务序列...\n');
    
    % Simple task sequence: pick and place each block
    task_sequence = struct();
    task_sequence.current_task = 1;
    task_sequence.tasks = [];
    
    % Create tasks for each block
    for i = 1:lego_blocks.count
        task = struct();
        task.block_id = i;
        if i <= 6
            task.arm = "left";  % First 6 blocks for left arm
        else
            task.arm = "right"; % Last 6 blocks for right arm
        end
        task.pick_pos = lego_blocks.positions(i, :, 1);
        task.place_pos = target_positions(i, :);
        task.status = "pending";
        task.start_time = (i-1) * 2.5;  % 2.5 seconds per task
        task.duration = 2.0;  % 2 seconds to complete each task
        
        task_sequence.tasks = [task_sequence.tasks; task];
    end
    
    fprintf('✓ 任务序列创建完成\n');
    fprintf('  - 任务总数: %d\n', length(task_sequence.tasks));
    fprintf('  - 左臂任务: %d\n', sum([task_sequence.tasks.arm] == "left"));
    fprintf('  - 右臂任务: %d\n', sum([task_sequence.tasks.arm] == "right"));
    
    %% 7. Run simulation
    fprintf('\n7. 开始仿真...\n');
    
    % Initialize visualization
    figure('Name', 'YuMi LEGO Stacking Simulation', 'Position', [100, 100, 1200, 800]);
    hold on;
    
    % Simulation loop
    for step = 2:n_steps
        current_time = time_steps(step);
        
        % Update task status
        for i = 1:length(task_sequence.tasks)
            task = task_sequence.tasks(i);
            if strcmp(task.status, "pending") && current_time >= task.start_time
                task_sequence.tasks(i).status = "active";
                fprintf('  开始任务 %d: %s臂拾取积木 %d\n', i, task.arm, task.block_id);
            elseif strcmp(task.status, "active") && current_time >= (task.start_time + task.duration)
                task_sequence.tasks(i).status = "completed";
                fprintf('  完成任务 %d: 积木 %d 已放置\n', i, task.block_id);
            end
        end
        
        % Update robot states (simplified trajectory)
        robot_states.q_left(:, step) = robot_states.q_left(:, step-1);
        robot_states.q_right(:, step) = robot_states.q_right(:, step-1);
        
        % Update end-effector positions (simplified)
        robot_states.ee_pos_left(:, step) = robot_states.ee_pos_left(:, step-1);
        robot_states.ee_pos_right(:, step) = robot_states.ee_pos_right(:, step-1);
        
        % Update LEGO block positions
        for i = 1:lego_blocks.count
            % Copy previous position
            lego_blocks.positions(i, :, step) = lego_blocks.positions(i, :, step-1);
            lego_blocks.velocities(i, :, step) = lego_blocks.velocities(i, :, step-1);
            lego_blocks.orientations(i, :, step) = lego_blocks.orientations(i, :, step-1);
            lego_blocks.states(i, step) = lego_blocks.states(i, step-1);
            
            % Check if block is being moved
            task_idx = find([task_sequence.tasks.block_id] == i);
            if ~isempty(task_idx)
                task = task_sequence.tasks(task_idx);
                if strcmp(task.status, "active")
                    % Interpolate position during task execution
                    task_progress = (current_time - task.start_time) / task.duration;
                    task_progress = max(0, min(1, task_progress));
                    
                    % Simple linear interpolation
                    start_pos = task.pick_pos;
                    end_pos = task.place_pos;
                    current_pos = start_pos + task_progress * (end_pos - start_pos);
                    
                    lego_blocks.positions(i, :, step) = current_pos;
                    lego_blocks.states(i, step) = "moving";
                elseif strcmp(task.status, "completed")
                    lego_blocks.positions(i, :, step) = task.place_pos;
                    lego_blocks.states(i, step) = "placed";
                end
            end
        end
        
        % Simple physics: apply gravity to free blocks
        for i = 1:lego_blocks.count
            if lego_blocks.states(i, step) == "free"
                % Apply gravity
                lego_blocks.velocities(i, 3, step) = lego_blocks.velocities(i, 3, step-1) + gravity(3) * dt;
                
                % Update position
                lego_blocks.positions(i, :, step) = lego_blocks.positions(i, :, step-1) + ...
                    lego_blocks.velocities(i, :, step) * dt;
                
                % Ground collision
                if lego_blocks.positions(i, 3, step) <= 0.06  % Table height
                    lego_blocks.positions(i, 3, step) = 0.06;
                    lego_blocks.velocities(i, 3, step) = 0;
                end
            end
        end
        
        % Progress indicator
        if mod(step, round(n_steps/10)) == 0
            progress = step / n_steps * 100;
            fprintf('  仿真进度: %.1f%%\n', progress);
        end
    end
    
    fprintf('✓ 仿真完成\n');
    
    %% 8. Generate results
    fprintf('\n8. 生成仿真结果...\n');
    
    sim_results = struct();
    sim_results.time = time_steps;
    sim_results.robot_states = robot_states;
    sim_results.lego_blocks = lego_blocks;
    sim_results.task_sequence = task_sequence;
    sim_results.target_positions = target_positions;
    sim_results.simulation_params = struct('dt', dt, 'gravity', gravity, 'friction', friction_coeff);
    
    % Calculate final positions accuracy
    final_positions = lego_blocks.positions(:, :, end);
    position_errors = zeros(lego_blocks.count, 1);
    
    for i = 1:lego_blocks.count
        error = norm(final_positions(i, :) - target_positions(i, :));
        position_errors(i) = error;
    end
    
    sim_results.position_errors = position_errors;
    sim_results.mean_error = mean(position_errors);
    sim_results.max_error = max(position_errors);
    
    % Save results
    save('simplified_simulation_results.mat', 'sim_results');
    
    fprintf('✓ 仿真结果生成完成\n');
    fprintf('  - 平均位置误差: %.4f m\n', sim_results.mean_error);
    fprintf('  - 最大位置误差: %.4f m\n', sim_results.max_error);
    fprintf('  - 完成任务数: %d/%d\n', sum([task_sequence.tasks.status] == "completed"), length(task_sequence.tasks));
    
    %% 9. Create visualization
    fprintf('\n9. 创建可视化...\n');
    
    % Plot final configuration
    clf;
    [yumi, qHome, table, ax] = setupRobotEnv_fixed();
    hold on;
    
    % Plot final LEGO positions
    for i = 1:lego_blocks.count
        final_pos = final_positions(i, :);
        target_pos = target_positions(i, :);
        
        % Plot actual position
        plot3(final_pos(1), final_pos(2), final_pos(3), 'ro', 'MarkerSize', 8, 'MarkerFaceColor', 'r');
        
        % Plot target position
        plot3(target_pos(1), target_pos(2), target_pos(3), 'go', 'MarkerSize', 8, 'MarkerFaceColor', 'g');
        
        % Draw error line
        plot3([final_pos(1), target_pos(1)], [final_pos(2), target_pos(2)], ...
              [final_pos(3), target_pos(3)], 'k--', 'LineWidth', 1);
        
        % Add labels
        text(final_pos(1), final_pos(2), final_pos(3) + 0.01, sprintf('B%02d', i), ...
             'HorizontalAlignment', 'center', 'FontSize', 8);
    end
    
    legend('实际位置', '目标位置', '位置误差', 'Location', 'best');
    title('YuMi LEGO 第一层堆叠仿真结果', 'FontSize', 14, 'FontWeight', 'bold');
    
    % Save visualization
    saveas(gcf, 'simplified_simulation_result.png');
    
    fprintf('✓ 可视化创建完成\n');
    
    %% Summary
    fprintf('\n=== 简化物理仿真总结 ===\n');
    fprintf('仿真类型: 纯 MATLAB 实现\n');
    fprintf('仿真时间: %.1f 秒\n', t_total);
    fprintf('积木数量: %d\n', lego_blocks.count);
    fprintf('任务完成率: %.1f%%\n', sum([task_sequence.tasks.status] == "completed") / length(task_sequence.tasks) * 100);
    fprintf('平均精度: %.2f mm\n', sim_results.mean_error * 1000);
    fprintf('状态: 成功完成\n');
    fprintf('================================\n');
end
