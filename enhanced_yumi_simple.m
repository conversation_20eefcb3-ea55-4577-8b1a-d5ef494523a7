function [sim_results] = enhanced_yumi_simple()
    % 简化版增强YuMi仿真 - 测试基本功能
    
    fprintf('=== 简化版增强 YuMi LEGO 仿真测试 ===\n\n');
    
    %% 1. 基本参数设置
    fprintf('1. 初始化基本参数...\n');
    
    dt = 0.1;  % 时间步长
    t_total = 30;  % 总时间
    time_steps = 0:dt:t_total;
    n_steps = length(time_steps);
    
    % 坐标系旋转矩阵（90度）
    coord_rotation = [0 1 0; -1 0 0; 0 0 1];
    
    fprintf('✓ 基本参数设置完成\n');
    
    %% 2. 加载机器人环境
    fprintf('\n2. 加载机器人环境...\n');
    
    try
        [yumi, qHome, table, ax] = setupRobotEnv_fixed();
        fprintf('✓ 机器人环境加载成功\n');
    catch ME
        fprintf('✗ 机器人环境加载失败: %s\n', ME.message);
        return;
    end
    
    %% 3. 初始化LEGO积木（增强版）
    fprintf('\n3. 初始化增强版LEGO积木...\n');
    
    lego_blocks = struct();
    lego_blocks.count = 12;
    lego_blocks.size = [0.0636, 0.0318, 0.0400];  % 增大尺寸
    lego_blocks.mass = 0.004;
    
    % 颜色配置
    lego_blocks.colors = zeros(12, 3);
    for i = 1:6
        lego_blocks.colors(i, :) = [1.0, 0.2, 0.2];  % 红色 - 左臂
    end
    for i = 7:12
        lego_blocks.colors(i, :) = [0.2, 0.2, 1.0];  % 蓝色 - 右臂
    end
    
    % 初始化位置
    lego_blocks.positions = zeros(lego_blocks.count, 3, n_steps);
    lego_blocks.states = strings(lego_blocks.count, n_steps);
    
    % 设置初始位置（应用坐标系旋转）
    for i = 1:6  % 左臂积木
        pos_original = [-0.1 + (i-1)*0.04, 0.2, 0.07];
        pos_rotated = (coord_rotation * pos_original')';
        lego_blocks.positions(i, :, 1) = pos_rotated;
        lego_blocks.states(i, 1) = "free";
    end
    
    for i = 7:12  % 右臂积木
        pos_original = [-0.1 + (i-7)*0.04, -0.2, 0.07];
        pos_rotated = (coord_rotation * pos_original')';
        lego_blocks.positions(i, :, 1) = pos_rotated;
        lego_blocks.states(i, 1) = "free";
    end
    
    fprintf('✓ 增强版LEGO积木初始化完成\n');
    fprintf('  - 积木数量: %d\n', lego_blocks.count);
    fprintf('  - 增强尺寸: [%.4f, %.4f, %.4f] m\n', lego_blocks.size);
    
    %% 4. 定义目标位置
    fprintf('\n4. 定义目标位置...\n');
    
    % 原始目标位置
    target_positions_original = [
        0.0000, 0.4125, 0.0648;
        0.0000, 0.5875, 0.0648;
        -0.0080, 0.4364, 0.0648;
        -0.0080, 0.5636, 0.0648;
        0.0080, 0.4364, 0.0648;
        0.0080, 0.5636, 0.0648;
        -0.0080, 0.4682, 0.0648;
        -0.0080, 0.5318, 0.0648;
        0.0080, 0.4682, 0.0648;
        0.0080, 0.5318, 0.0648;
        -0.0080, 0.5000, 0.0648;
        0.0080, 0.5000, 0.0648
    ];
    
    % 应用坐标系旋转
    target_positions = zeros(size(target_positions_original));
    for i = 1:size(target_positions_original, 1)
        target_positions(i, :) = (coord_rotation * target_positions_original(i, :)')';
    end
    
    fprintf('✓ 目标位置定义完成\n');
    
    %% 5. 创建任务序列（交替执行）
    fprintf('\n5. 创建任务序列...\n');
    
    task_sequence = struct();
    task_sequence.tasks = [];
    
    % 交替执行顺序
    task_order = [1, 7, 2, 8, 3, 9, 4, 10, 5, 11, 6, 12];
    
    for idx = 1:length(task_order)
        i = task_order(idx);
        task = struct();
        task.block_id = i;
        
        if i <= 6
            task.arm = "left";
        else
            task.arm = "right";
        end
        
        task.pick_pos = lego_blocks.positions(i, :, 1);
        task.place_pos = target_positions(i, :);
        task.status = "pending";
        task.start_time = (idx-1) * 2.5;
        task.duration = 2.0;
        task.color = lego_blocks.colors(i, :);
        
        task_sequence.tasks = [task_sequence.tasks; task];
    end
    
    fprintf('✓ 任务序列创建完成\n');
    fprintf('  - 任务总数: %d\n', length(task_sequence.tasks));
    fprintf('  - 执行模式: 左右臂交替\n');
    
    %% 6. 创建可视化
    fprintf('\n6. 创建增强版可视化...\n');
    
    figure('Name', '增强版 YuMi LEGO 仿真', 'Position', [100, 100, 1200, 800]);
    clf;
    
    % 重新设置环境
    [yumi, qHome, table, ax] = setupRobotEnv_fixed();
    hold on;
    
    % 绘制增强版积木
    block_handles = [];
    for i = 1:lego_blocks.count
        pos = lego_blocks.positions(i, :, 1);
        
        % 创建增大的积木
        [X, Y, Z] = create_enhanced_block(lego_blocks.size);
        
        h = surf(X + pos(1), Y + pos(2), Z + pos(3), ...
                'FaceColor', lego_blocks.colors(i, :), ...
                'EdgeColor', 'k', 'LineWidth', 1.5, ...
                'FaceAlpha', 0.8);
        block_handles = [block_handles; h];
        
        % 添加标签
        text(pos(1), pos(2), pos(3) + lego_blocks.size(3)/2 + 0.01, ...
             sprintf('B%02d', i), 'HorizontalAlignment', 'center', ...
             'FontSize', 10, 'FontWeight', 'bold');
    end
    
    % 绘制目标位置
    for i = 1:size(target_positions, 1)
        pos = target_positions(i, :);
        plot3(pos(1), pos(2), pos(3), 'go', 'MarkerSize', 8, 'MarkerFaceColor', 'g');
        text(pos(1), pos(2), pos(3) + 0.02, sprintf('T%02d', i), ...
             'HorizontalAlignment', 'center', 'FontSize', 8, 'Color', 'g');
    end
    
    % 设置视角和标题
    view(45, 30);
    axis equal;
    grid on;
    title('增强版 YuMi LEGO 城堡堆叠仿真 - 坐标系修正版', 'FontSize', 14, 'FontWeight', 'bold');
    
    % 添加图例
    legend('YuMi机器人', '工作台', '左臂积木(红色)', '右臂积木(蓝色)', '目标位置', 'Location', 'best');
    
    fprintf('✓ 增强版可视化创建完成\n');
    
    %% 7. 简单仿真循环
    fprintf('\n7. 执行简单仿真...\n');
    
    for step = 2:n_steps
        current_time = time_steps(step);
        
        % 更新任务状态
        for i = 1:length(task_sequence.tasks)
            task = task_sequence.tasks(i);
            if strcmp(task.status, "pending") && current_time >= task.start_time
                task_sequence.tasks(i).status = "active";
                fprintf('  [%.1fs] 开始: %s臂 → B%02d\n', current_time, upper(task.arm), task.block_id);
            elseif strcmp(task.status, "active") && current_time >= (task.start_time + task.duration)
                task_sequence.tasks(i).status = "completed";
                fprintf('  [%.1fs] 完成: B%02d已放置\n', current_time, task.block_id);
            end
        end
        
        % 更新积木位置
        for i = 1:lego_blocks.count
            lego_blocks.positions(i, :, step) = lego_blocks.positions(i, :, step-1);
            lego_blocks.states(i, step) = lego_blocks.states(i, step-1);
            
            % 检查任务
            task_idx = find([task_sequence.tasks.block_id] == i);
            if ~isempty(task_idx)
                task = task_sequence.tasks(task_idx);
                if strcmp(task.status, "active")
                    % 简单线性插值
                    progress = (current_time - task.start_time) / task.duration;
                    progress = max(0, min(1, progress));
                    
                    start_pos = task.pick_pos;
                    end_pos = task.place_pos;
                    current_pos = start_pos + progress * (end_pos - start_pos);
                    
                    lego_blocks.positions(i, :, step) = current_pos;
                    lego_blocks.states(i, step) = "moving";
                elseif strcmp(task.status, "completed")
                    lego_blocks.positions(i, :, step) = task.place_pos;
                    lego_blocks.states(i, step) = "placed";
                end
            end
        end
        
        % 进度显示
        if mod(step, round(n_steps/10)) == 0
            progress = step / n_steps * 100;
            fprintf('  仿真进度: %.1f%%\n', progress);
        end
    end
    
    fprintf('✓ 简单仿真完成\n');
    
    %% 8. 更新最终可视化
    fprintf('\n8. 更新最终可视化...\n');
    
    % 更新积木位置到最终状态
    for i = 1:lego_blocks.count
        pos = lego_blocks.positions(i, :, end);
        [X, Y, Z] = create_enhanced_block(lego_blocks.size);
        
        set(block_handles(i), 'XData', X + pos(1), ...
                             'YData', Y + pos(2), ...
                             'ZData', Z + pos(3));
    end
    
    % 保存结果
    saveas(gcf, 'enhanced_yumi_simple_result.png');
    
    fprintf('✓ 最终可视化更新完成\n');
    
    %% 9. 生成结果
    sim_results = struct();
    sim_results.time = time_steps;
    sim_results.lego_blocks = lego_blocks;
    sim_results.task_sequence = task_sequence;
    sim_results.target_positions = target_positions;
    sim_results.coord_rotation = coord_rotation;
    
    % 计算精度
    final_positions = lego_blocks.positions(:, :, end);
    position_errors = zeros(lego_blocks.count, 1);
    for i = 1:lego_blocks.count
        error = norm(final_positions(i, :) - target_positions(i, :));
        position_errors(i) = error;
    end
    
    sim_results.position_errors = position_errors;
    sim_results.mean_error = mean(position_errors);
    sim_results.max_error = max(position_errors);
    
    save('enhanced_simple_results.mat', 'sim_results');
    
    fprintf('\n=== 简化版增强仿真完成 ===\n');
    fprintf('坐标系修正: ✓ (旋转90度)\n');
    fprintf('积木增强: ✓ (2倍尺寸 + 颜色区分)\n');
    fprintf('任务交替: ✓ (左右臂交替执行)\n');
    fprintf('平均误差: %.4f m\n', sim_results.mean_error);
    fprintf('完成率: %.1f%%\n', sum([task_sequence.tasks.status] == "completed") / length(task_sequence.tasks) * 100);
    fprintf('状态: 测试成功！\n');
    fprintf('===============================\n');
    
end

function [X, Y, Z] = create_enhanced_block(size)
    % 创建增强版积木的简单3D模型
    
    % 创建长方体的6个面
    x = [-size(1)/2, size(1)/2, size(1)/2, -size(1)/2];
    y = [-size(2)/2, -size(2)/2, size(2)/2, size(2)/2];
    z_bottom = [0, 0, 0, 0];
    z_top = [size(3), size(3), size(3), size(3)];
    
    % 组合成surf格式
    X = [x; x];
    Y = [y; y];
    Z = [z_bottom; z_top];
end
