function [sim_results] = optimized_yumi_animation()
    % 高性能优化版 YuMi LEGO 城堡堆叠动画仿真
    % 主要优化：渲染效率、内存使用、帧率控制
    
    fprintf('=== 高性能 YuMi LEGO 城堡堆叠动画 ===\n\n');
    
    %% 1. 优化的初始化参数
    fprintf('1. 初始化参数...\n');
    dt = 0.04;  % 25 FPS
    t_total = 120;
    target_fps = 25;
    frame_interval = 1/target_fps;
    video_filename = 'yumi_lego_optimized.mp4';
    
    % 性能监控
    perf_monitor = struct();
    perf_monitor.frame_times = [];
    perf_monitor.render_times = [];
    fprintf('✓ 优化参数初始化完成\n');

    %% 2. 加载环境和数据
    fprintf('\n2. 加载环境和数据...\n');
    try
        [yumi, qHome, ~, ax] = setupRobotEnv_fixed();
        fprintf('✓ 机器人环境加载成功\n');
    catch ME
        fprintf('✗ 机器人环境加载失败: %s\n', ME.message); return;
    end
    
    try
        opts = detectImportOptions('積木座標.csv(1).xlsx');
        opts.VariableNamingRule = 'preserve';
        lego_data = readtable('積木座標.csv(1).xlsx', opts);
        fprintf('✓ 积木坐标加载成功\n');
    catch ME
        fprintf('✗ 无法加载积木坐标: %s\n', ME.message); return;
    end

    %% 3. 优化的LEGO积木初始化
    fprintf('\n3. 初始化LEGO积木（优化版）...\n');
    lego_data = lego_data(~ismissing(lego_data.("Brick ID")), :);
    num_blocks = height(lego_data) / 2;
    
    % 优化：只存储当前状态，不预分配所有时间步
    lego_blocks = struct();
    lego_blocks.count = num_blocks;
    lego_blocks.current_positions = zeros(num_blocks, 3);
    lego_blocks.target_positions = zeros(num_blocks, 3);
    lego_blocks.start_positions = zeros(num_blocks, 3);
    lego_blocks.states = strings(num_blocks, 1);
    lego_blocks.ids = strings(num_blocks, 1);
    lego_blocks.sizes = zeros(num_blocks, 3);
    lego_blocks.colors = zeros(num_blocks, 3);
    lego_blocks.is_moving = false(num_blocks, 1);  % 移动标记
    
    % 预计算所有积木的网格数据
    lego_blocks.mesh_vertices = cell(num_blocks, 1);
    lego_blocks.mesh_faces = cell(num_blocks, 1);
    
    for i = 1:num_blocks
        start_row = lego_data(2*i-1, :);
        end_row = lego_data(2*i, :);
        
        lego_blocks.ids(i) = extractBefore(start_row.("Brick ID"){1}, '_start');
        brick_type_str = start_row.Type{1};
        lego_blocks.sizes(i, :) = parse_brick_type(brick_type_str);
        
        lego_blocks.start_positions(i, :) = [start_row.('X (m)'), start_row.('Y (m)'), start_row.('Z (m)')];
        lego_blocks.target_positions(i, :) = [end_row.('X (m)'), end_row.('Y (m)'), end_row.('Z (m)')];
        lego_blocks.current_positions(i, :) = lego_blocks.start_positions(i, :);
        lego_blocks.states(i) = "free";
        
        % 预计算网格
        [vertices, faces] = create_lego_block_mesh(lego_blocks.sizes(i, :));
        lego_blocks.mesh_vertices{i} = vertices;
        lego_blocks.mesh_faces{i} = faces;
        
        % 颜色分配
        if lego_blocks.start_positions(i, 2) > 0
            lego_blocks.colors(i, :) = [0.9, 0.2, 0.2];
        else
            lego_blocks.colors(i, :) = [0.2, 0.2, 0.9];
        end
    end
    fprintf('✓ %d个积木初始化完成（网格预计算）\n', num_blocks);

    %% 4. 创建任务序列
    fprintf('\n4. 创建任务序列...\n');
    task_sequence = create_optimized_task_sequence(lego_blocks);
    fprintf('✓ %d个任务创建完成\n', length(task_sequence));

    %% 5. 优化的图形环境创建
    fprintf('\n5. 创建优化图形环境...\n');
    fig = gcf;
    
    % 创建高效的图形对象
    graphics_objects = create_optimized_graphics(lego_blocks, ax);
    
    % 状态显示
    status_text = text(-0.1, -0.4, 0.5, '初始化...', 'FontSize', 14, 'FontWeight', 'bold', 'Parent', ax);
    title(ax, 'YuMi LEGO 高性能动画 | 准备中...', 'FontSize', 16);
    
    % 设置图形优化选项
    set(fig, 'Renderer', 'opengl');  % 使用硬件加速
    set(ax, 'SortMethod', 'depth');   % 优化深度排序
    fprintf('✓ 优化图形环境创建完成\n');
    
    %% 6. 视频录制准备
    fprintf('\n6. 准备视频录制...\n');
    video_writer = VideoWriter(video_filename, 'MPEG-4');
    video_writer.FrameRate = target_fps;
    video_writer.Quality = 90;  % 降低质量以提高性能
    open(video_writer);
    fprintf('✓ 视频录制准备就绪\n');

    %% 7. 优化的主动画循环
    fprintf('\n7. 开始高性能动画仿真...\n\n');
    
    % IK 求解器初始化
    ikLeft = inverseKinematics('RigidBodyTree', yumi, 'SolverAlgorithm', 'LevenbergMarquardt');
    ikRight = inverseKinematics('RigidBodyTree', yumi, 'SolverAlgorithm', 'LevenbergMarquardt');
    weights = [0.25 0.25 0.25 1 1 1];
    
    q_current = qHome';
    current_time = 0;
    last_render_time = 0;
    frame_count = 0;
    
    % 主循环
    tic;
    while current_time < t_total
        frame_start_time = tic;
        
        % 任务状态更新（优化：只在需要时更新）
        [task_sequence, active_task, task_changed] = update_task_status_optimized(task_sequence, current_time);
        
        % 轨迹与IK计算（只在有活动任务时）
        if ~isempty(active_task)
            progress = min(1, (current_time - active_task.start_time) / active_task.duration);
            block_id = active_task.block_id;
            
            [T_gripper, new_block_pos] = calculate_trajectory_optimized(active_task, progress, lego_blocks.sizes(block_id, :));
            
            % 更新积木位置（只更新移动的积木）
            lego_blocks.current_positions(block_id, :) = new_block_pos;
            lego_blocks.states(block_id) = "moving";
            lego_blocks.is_moving(block_id) = true;
            
            % IK求解（缓存结果）
            if strcmp(active_task.arm, 'left')
                [q_sol, solInfo] = ikLeft('gripper_l_base', T_gripper, weights, q_current');
                if strcmp(solInfo.Status, 'success')
                    q_current(1:7) = q_sol(1:7);
                end
            else
                [q_sol, solInfo] = ikRight('gripper_r_base', T_gripper, weights, q_current');
                if strcmp(solInfo.Status, 'success')
                    q_current(8:14) = q_sol(8:14);
                end
            end
        else
            % 标记所有积木为静止
            lego_blocks.is_moving(:) = false;
        end
        
        % 帧率控制的渲染
        if (current_time - last_render_time) >= frame_interval
            render_start = tic;
            
            % 高效动画更新
            update_animation_optimized(fig, video_writer, yumi, q_current, lego_blocks, ...
                                     graphics_objects, status_text, task_sequence, current_time, active_task);
            
            last_render_time = current_time;
            frame_count = frame_count + 1;
            
            % 性能监控
            render_time = toc(render_start);
            perf_monitor.render_times(end+1) = render_time;
            
            % 每100帧显示性能信息
            if mod(frame_count, 100) == 0
                avg_render_time = mean(perf_monitor.render_times(max(1,end-99):end));
                fprintf('帧 %d: 平均渲染时间 %.3fs, 实际FPS: %.1f\n', ...
                        frame_count, avg_render_time, 1/avg_render_time);
            end
        end
        
        % 时间步进
        current_time = current_time + dt;
        
        % 记录帧时间
        frame_time = toc(frame_start_time);
        perf_monitor.frame_times(end+1) = frame_time;
    end
    
    total_time = toc;
    close(video_writer);
    
    %% 8. 性能报告
    fprintf('\n\n=== 性能报告 ===\n');
    fprintf('总仿真时间: %.2f秒\n', total_time);
    fprintf('总帧数: %d\n', frame_count);
    fprintf('平均FPS: %.1f\n', frame_count / total_time);
    fprintf('平均帧时间: %.3fs\n', mean(perf_monitor.frame_times));
    fprintf('平均渲染时间: %.3fs\n', mean(perf_monitor.render_times));
    
    % 保存结果
    sim_results = struct();
    sim_results.performance = perf_monitor;
    sim_results.total_time = total_time;
    sim_results.frame_count = frame_count;
    sim_results.lego_blocks = lego_blocks;
    
    save('optimized_simulation_results.mat', 'sim_results');
    saveas(fig, 'optimized_simulation_screenshot.png');
    fprintf('\n✅ 优化仿真完成! 视频已保存至 %s\n', video_filename);
end

% === 优化的辅助函数 ===

function tasks = create_optimized_task_sequence(lego_blocks)
    tasks = [];
    left_indices = find(lego_blocks.start_positions(:, 2) > 0);
    right_indices = find(lego_blocks.start_positions(:, 2) <= 0);
    
    % 交替分配任务
    task_order = [];
    max_len = max(length(left_indices), length(right_indices));
    for i = 1:max_len
        if i <= length(left_indices), task_order = [task_order, left_indices(i)]; end
        if i <= length(right_indices), task_order = [task_order, right_indices(i)]; end
    end

    for idx = 1:length(task_order)
        i = task_order(idx);
        task.block_id = i;
        task.arm = iif(lego_blocks.start_positions(i, 2) > 0, "left", "right");
        task.pick_pos = lego_blocks.start_positions(i, :);
        task.place_pos = lego_blocks.target_positions(i, :);
        task.status = "pending";
        task.start_time = (idx-1) * 5.0;
        task.duration = 4.5;
        task.color = lego_blocks.colors(i, :);
        task.id_str = lego_blocks.ids(i);
        tasks = [tasks; task];
    end
end

function [task_sequence, active_task, changed] = update_task_status_optimized(task_sequence, current_time)
    persistent last_update_time last_active_id;
    if isempty(last_update_time), last_update_time = -1; last_active_id = -1; end
    
    active_task = [];
    changed = false;
    
    % 只在时间显著变化时更新
    if abs(current_time - last_update_time) < 0.1 && last_active_id > 0
        % 快速查找当前活动任务
        if last_active_id <= length(task_sequence) && strcmp(task_sequence(last_active_id).status, "active")
            active_task = task_sequence(last_active_id);
            return;
        end
    end
    
    for i = 1:length(task_sequence)
        old_status = task_sequence(i).status;
        
        if strcmp(task_sequence(i).status, "pending") && current_time >= task_sequence(i).start_time
            task_sequence(i).status = "active";
            changed = true;
        elseif strcmp(task_sequence(i).status, "active") && current_time >= (task_sequence(i).start_time + task_sequence(i).duration)
            task_sequence(i).status = "completed";
            changed = true;
        end
        
        if strcmp(task_sequence(i).status, "active")
            active_task = task_sequence(i);
            last_active_id = i;
        end
    end
    
    last_update_time = current_time;
end

function [T_gripper, current_pos] = calculate_trajectory_optimized(active_task, progress, block_size)
    % 优化的轨迹计算，减少重复计算
    persistent trajectory_cache;
    if isempty(trajectory_cache), trajectory_cache = containers.Map(); end

    % 缓存键
    cache_key = sprintf('%d_%.3f', active_task.block_id, progress);
    if isKey(trajectory_cache, cache_key)
        cached_result = trajectory_cache(cache_key);
        T_gripper = cached_result.T_gripper;
        current_pos = cached_result.current_pos;
        return;
    end

    hover_h = 0.1;
    p_grasp = 0.2; p_lift = 0.35; p_move = 0.85;

    start_pos = active_task.pick_pos;
    end_pos = active_task.place_pos;

    is_attached = false;
    gripper_target = zeros(1,3);

    if progress <= p_grasp
        p = progress / p_grasp;
        gripper_target = (start_pos + [0 0 hover_h]) * (1-p) + start_pos * p;
    elseif progress <= p_lift
        is_attached = true;
        p = (progress - p_grasp) / (p_lift - p_grasp);
        gripper_target = start_pos * (1-p) + (start_pos + [0 0 hover_h]) * p;
    elseif progress <= p_move
        is_attached = true;
        p = (progress - p_lift) / (p_move - p_lift);
        gripper_target = (start_pos + [0 0 hover_h]) * (1-p) + (end_pos + [0 0 hover_h]) * p;
    else
        is_attached = true;
        p = (progress - p_move) / (1 - p_move);
        gripper_target = (end_pos + [0 0 hover_h]) * (1-p) + end_pos * p;
    end

    current_pos = iif(is_attached, gripper_target, start_pos);
    T_gripper = trvec2tform(gripper_target + [0 0 block_size(3)/2]);
    T_gripper(1:3, 1:3) = [1 0 0; 0 -1 0; 0 0 -1];

    % 缓存结果（限制缓存大小）
    if length(trajectory_cache) < 1000
        result.T_gripper = T_gripper;
        result.current_pos = current_pos;
        trajectory_cache(cache_key) = result;
    end
end

function graphics_objects = create_optimized_graphics(lego_blocks, ax)
    % 创建优化的图形对象
    graphics_objects = struct();
    graphics_objects.block_handles = [];
    graphics_objects.robot_handles = [];

    % 创建积木图形对象
    for i = 1:lego_blocks.count
        pos = lego_blocks.current_positions(i, :);
        vertices = lego_blocks.mesh_vertices{i} + pos;
        faces = lego_blocks.mesh_faces{i};

        h = patch(ax, 'Vertices', vertices, 'Faces', faces, ...
                  'FaceColor', lego_blocks.colors(i, :), ...
                  'EdgeColor', 'k', 'LineWidth', 0.5, ...
                  'FaceLighting', 'gouraud', 'AmbientStrength', 0.3);
        graphics_objects.block_handles = [graphics_objects.block_handles; h];
    end

    % 设置光照
    light(ax, 'Position', [1 1 1], 'Style', 'infinite');
end

function update_animation_optimized(fig, writer, robot, q, blocks, graphics_obj, status_h, tasks, time, active_task)
    % 高性能动画更新函数 - 修复重影问题
    persistent last_robot_update frame_skip_counter current_ax;
    if isempty(last_robot_update), last_robot_update = -1; frame_skip_counter = 0; end

    % 获取当前坐标轴
    if isempty(current_ax)
        current_ax = gca;
    end

    % 帧跳过策略：每3帧更新一次机器人
    frame_skip_counter = frame_skip_counter + 1;
    update_robot = (frame_skip_counter >= 3);

    if update_robot
        % 修复重影：正确的机器人更新方式
        % 清除之前的机器人显示，避免重影
        cla(current_ax); % 清除坐标轴内容

        % 重新创建积木图形（因为cla清除了所有内容）
        recreate_block_graphics(blocks, graphics_obj, current_ax);

        % 显示机器人（不保留之前的图形）
        show(robot, q', 'Visuals', 'on', 'Collisions', 'off', 'PreservePlot', false, 'Parent', current_ax);

        frame_skip_counter = 0;
        last_robot_update = time;
    end

    % 更新移动中的积木（如果没有重新创建）
    if ~update_robot
        moving_blocks = find(blocks.is_moving);
        for i = moving_blocks'
            new_vertices = blocks.mesh_vertices{i} + blocks.current_positions(i, :);
            set(graphics_obj.block_handles(i), 'Vertices', new_vertices);
        end
    end

    % 更新状态文本（降低频率）
    if mod(frame_skip_counter, 5) == 0
        if ~isempty(active_task)
            status_str = sprintf('时间: %.1fs | %s臂 → %s', time, upper(active_task.arm), active_task.id_str);
        else
            status_str = sprintf('时间: %.1fs | 等待任务...', time);
        end
        set(status_h, 'String', status_str);

        completed_count = sum(arrayfun(@(x) strcmp(x.status, 'completed'), tasks));
        title(gca, sprintf('YuMi LEGO 高性能动画 | 已完成: %d/%d | FPS优化', completed_count, blocks.count));
    end

    % 减少drawnow调用
    if frame_skip_counter == 1
        drawnow limitrate;  % 限制刷新率
    end

    % 视频录制（每帧）
    try
        frame = getframe(fig);
        writeVideo(writer, frame);
    catch ME
        fprintf('视频帧写入失败: %s\n', ME.message);
    end
end

function size_vec = parse_brick_type(type_str)
    persistent stud_pitch brick_height;
    if isempty(stud_pitch)
        stud_pitch = 0.008; brick_height = 0.0096;
    end

    dims = sscanf(lower(type_str), '%dx%d');
    if length(dims) == 2
        w = dims(1) * stud_pitch - 0.0002;
        d = dims(2) * stud_pitch - 0.0002;
        size_vec = iif(contains(type_str, 'Vert', 'IgnoreCase', true), [d, w, brick_height], [w, d, brick_height]);
    else
        size_vec = [0.0318, 0.0158, 0.0096]; % Default
    end
end

function [v, f] = create_lego_block_mesh(s)
    l=s(1)/2; w=s(2)/2; h=s(3);
    v = [-l -w 0; l -w 0; l w 0; -l w 0; -l -w h; l -w h; l w h; -l w h];
    f = [1 2 3 4; 5 8 7 6; 1 5 6 2; 3 7 8 4; 1 4 8 5; 2 6 7 3];
end

function recreate_block_graphics(blocks, graphics_obj, ax)
    % 重新创建积木图形对象（解决重影问题）
    for i = 1:blocks.count
        pos = blocks.current_positions(i, :);
        vertices = blocks.mesh_vertices{i} + pos;
        faces = blocks.mesh_faces{i};

        % 重新创建patch对象
        graphics_obj.block_handles(i) = patch(ax, 'Vertices', vertices, 'Faces', faces, ...
                  'FaceColor', blocks.colors(i, :), ...
                  'EdgeColor', 'k', 'LineWidth', 0.5, ...
                  'FaceLighting', 'gouraud', 'AmbientStrength', 0.3);
    end

    % 重新设置光照
    light(ax, 'Position', [1 1 1], 'Style', 'infinite');
end

function out = iif(cond, true_val, false_val)
    if cond, out = true_val; else, out = false_val; end
end
