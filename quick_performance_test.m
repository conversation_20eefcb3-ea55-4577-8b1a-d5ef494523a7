function quick_performance_test()
    % 快速性能测试脚本
    % 用于验证优化效果，无需完整运行动画
    
    fprintf('=== YuMi动画快速性能测试 ===\n\n');
    
    %% 1. 测试环境准备
    fprintf('1. 准备测试环境...\n');
    
    % 检查必要文件
    required_files = {'optimized_yumi_animation.m', '積木座標.csv(1).xlsx'};
    missing_files = {};
    
    for i = 1:length(required_files)
        if ~exist(required_files{i}, 'file')
            missing_files{end+1} = required_files{i};
        end
    end
    
    if ~isempty(missing_files)
        fprintf('❌ 缺少必要文件:\n');
        for i = 1:length(missing_files)
            fprintf('   - %s\n', missing_files{i});
        end
        return;
    end
    
    fprintf('✓ 所有必要文件存在\n');
    
    %% 2. 基准性能测试
    fprintf('\n2. 执行基准性能测试...\n');
    
    % 测试图形渲染性能
    fprintf('测试图形渲染性能...\n');
    render_perf = test_rendering_performance();
    fprintf('✓ 渲染测试完成\n');
    
    % 测试数据处理性能
    fprintf('测试数据处理性能...\n');
    data_perf = test_data_processing_performance();
    fprintf('✓ 数据处理测试完成\n');
    
    % 测试内存使用
    fprintf('测试内存使用...\n');
    memory_perf = test_memory_usage();
    fprintf('✓ 内存测试完成\n');
    
    %% 3. 性能评估
    fprintf('\n3. 性能评估结果:\n');
    
    % 渲染性能评估
    fprintf('\n📊 渲染性能:\n');
    fprintf('   - 单帧渲染时间: %.3f秒\n', render_perf.single_frame_time);
    fprintf('   - 预期FPS: %.1f\n', 1/render_perf.single_frame_time);
    fprintf('   - 批量渲染效率: %.1f%%\n', render_perf.batch_efficiency);
    
    if render_perf.single_frame_time < 0.04
        fprintf('   ✅ 渲染性能: 优秀 (>25 FPS)\n');
    elseif render_perf.single_frame_time < 0.067
        fprintf('   ✅ 渲染性能: 良好 (15-25 FPS)\n');
    else
        fprintf('   ⚠️  渲染性能: 需要优化 (<15 FPS)\n');
    end
    
    % 数据处理性能评估
    fprintf('\n📊 数据处理性能:\n');
    fprintf('   - 轨迹计算时间: %.6f秒\n', data_perf.trajectory_time);
    fprintf('   - IK求解时间: %.6f秒\n', data_perf.ik_time);
    fprintf('   - 状态更新时间: %.6f秒\n', data_perf.state_update_time);
    
    % 内存使用评估
    fprintf('\n📊 内存使用:\n');
    fprintf('   - 基础内存: %.1f MB\n', memory_perf.base_memory);
    fprintf('   - 数据加载后: %.1f MB\n', memory_perf.after_data_load);
    fprintf('   - 图形创建后: %.1f MB\n', memory_perf.after_graphics);
    fprintf('   - 内存增长: %.1f MB\n', memory_perf.total_increase);
    
    %% 4. 优化建议
    fprintf('\n=== 优化建议 ===\n');
    
    if render_perf.single_frame_time > 0.04
        fprintf('🔧 渲染优化:\n');
        fprintf('   - 考虑使用更简化的图形模型\n');
        fprintf('   - 实现LOD (Level of Detail) 系统\n');
        fprintf('   - 使用硬件加速渲染\n');
    end
    
    if data_perf.ik_time > 0.01
        fprintf('🔧 计算优化:\n');
        fprintf('   - 缓存IK求解结果\n');
        fprintf('   - 使用更快的IK算法\n');
        fprintf('   - 减少IK求解频率\n');
    end
    
    if memory_perf.total_increase > 500
        fprintf('🔧 内存优化:\n');
        fprintf('   - 减少数据预分配\n');
        fprintf('   - 使用更紧凑的数据结构\n');
        fprintf('   - 实现数据流式处理\n');
    end
    
    %% 5. 保存测试结果
    test_results = struct();
    test_results.render_performance = render_perf;
    test_results.data_performance = data_perf;
    test_results.memory_performance = memory_perf;
    test_results.timestamp = datetime('now');
    
    save('quick_performance_test_results.mat', 'test_results');
    fprintf('\n💾 测试结果已保存至 quick_performance_test_results.mat\n');
    
    fprintf('\n✅ 快速性能测试完成！\n');
end

function perf = test_rendering_performance()
    % 测试渲染性能
    
    % 创建测试图形环境
    fig = figure('Visible', 'off');
    ax = axes(fig);
    
    % 创建测试积木
    num_test_blocks = 20;
    block_handles = [];
    
    for i = 1:num_test_blocks
        pos = [rand()*0.5, rand()*0.5, rand()*0.2];
        size_vec = [0.032, 0.016, 0.0096];
        [vertices, faces] = create_test_block_mesh(size_vec);
        
        h = patch(ax, 'Vertices', vertices + pos, 'Faces', faces, ...
                  'FaceColor', rand(1,3), 'EdgeColor', 'k');
        block_handles = [block_handles; h];
    end
    
    % 测试单帧渲染时间
    tic;
    for i = 1:num_test_blocks
        new_pos = [rand()*0.5, rand()*0.5, rand()*0.2];
        vertices = create_test_block_mesh([0.032, 0.016, 0.0096]) + new_pos;
        set(block_handles(i), 'Vertices', vertices);
    end
    drawnow;
    single_frame_time = toc;
    
    % 测试批量渲染效率
    num_frames = 10;
    tic;
    for frame = 1:num_frames
        for i = 1:num_test_blocks
            new_pos = [rand()*0.5, rand()*0.5, rand()*0.2];
            vertices = create_test_block_mesh([0.032, 0.016, 0.0096]) + new_pos;
            set(block_handles(i), 'Vertices', vertices);
        end
        if mod(frame, 3) == 0
            drawnow limitrate;
        end
    end
    batch_time = toc;
    
    close(fig);
    
    perf = struct();
    perf.single_frame_time = single_frame_time;
    perf.batch_time = batch_time;
    perf.batch_efficiency = (single_frame_time * num_frames) / batch_time * 100;
end

function perf = test_data_processing_performance()
    % 测试数据处理性能
    
    % 模拟轨迹计算
    start_pos = [0.3, 0.2, 0.1];
    end_pos = [0.5, -0.2, 0.3];
    block_size = [0.032, 0.016, 0.0096];
    
    tic;
    for i = 1:100
        progress = i/100;
        [~, ~] = calculate_test_trajectory(start_pos, end_pos, progress, block_size);
    end
    trajectory_time = toc / 100;
    
    % 模拟IK求解时间（简化版）
    tic;
    for i = 1:50
        % 模拟IK计算
        T = eye(4);
        T(1:3, 4) = [0.3 + rand()*0.2, 0.2 + rand()*0.2, 0.1 + rand()*0.2];
        % 这里应该是实际的IK求解，但为了测试简化
        q_result = rand(7, 1);
    end
    ik_time = toc / 50;
    
    % 模拟状态更新时间
    num_blocks = 50;
    states = strings(num_blocks, 1);
    positions = rand(num_blocks, 3);
    
    tic;
    for i = 1:100
        % 模拟状态更新
        for j = 1:num_blocks
            if rand() > 0.8
                states(j) = "moving";
                positions(j, :) = positions(j, :) + rand(1, 3) * 0.01;
            end
        end
    end
    state_update_time = toc / 100;
    
    perf = struct();
    perf.trajectory_time = trajectory_time;
    perf.ik_time = ik_time;
    perf.state_update_time = state_update_time;
end

function perf = test_memory_usage()
    % 测试内存使用
    
    % 获取基础内存
    base_memory = get_current_memory();
    
    % 模拟数据加载
    num_blocks = 100;
    positions = zeros(num_blocks, 3);
    states = strings(num_blocks, 1);
    mesh_data = cell(num_blocks, 1);
    
    for i = 1:num_blocks
        mesh_data{i} = rand(8, 3); % 模拟网格顶点数据
    end
    
    after_data_load = get_current_memory();
    
    % 模拟图形对象创建
    fig = figure('Visible', 'off');
    ax = axes(fig);
    handles = [];
    
    for i = 1:num_blocks
        h = patch(ax, 'Vertices', mesh_data{i}, 'Faces', [1 2 3 4; 5 6 7 8], ...
                  'FaceColor', rand(1,3));
        handles = [handles; h];
    end
    
    after_graphics = get_current_memory();
    
    close(fig);
    
    perf = struct();
    perf.base_memory = base_memory;
    perf.after_data_load = after_data_load;
    perf.after_graphics = after_graphics;
    perf.total_increase = after_graphics - base_memory;
end

function memory_mb = get_current_memory()
    % 获取当前MATLAB进程内存使用（简化版）
    try
        s = whos;
        total_bytes = sum([s.bytes]);
        memory_mb = total_bytes / (1024 * 1024);
    catch
        memory_mb = NaN;
    end
end

function [T_gripper, current_pos] = calculate_test_trajectory(start_pos, end_pos, progress, block_size)
    % 简化的轨迹计算（用于测试）
    hover_h = 0.1;
    
    if progress <= 0.5
        current_pos = start_pos + (end_pos - start_pos) * progress * 2;
        current_pos(3) = current_pos(3) + hover_h * sin(progress * pi);
    else
        current_pos = start_pos + (end_pos - start_pos) * progress;
    end
    
    T_gripper = eye(4);
    T_gripper(1:3, 4) = current_pos + [0 0 block_size(3)/2];
end

function [v, f] = create_test_block_mesh(s)
    % 创建测试积木网格
    l=s(1)/2; w=s(2)/2; h=s(3);
    v = [-l -w 0; l -w 0; l w 0; -l w 0; -l -w h; l -w h; l w h; -l w h];
    f = [1 2 3 4; 5 8 7 6; 1 5 6 2; 3 7 8 4; 1 4 8 5; 2 6 7 3];
end
