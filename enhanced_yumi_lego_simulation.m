function [sim_results] = enhanced_yumi_lego_simulation()
    % Enhanced YuMi LEGO Castle Stacking Simulation
    % 改进版 YuMi LEGO 城堡堆叠仿真系统
    % 
    % 主要改进：
    % 1. 坐标系修正 - 旋转90度确保左右臂正确分配
    % 2. LEGO积木可视化增强 - 增大尺寸、添加颜色、正确比例
    % 3. 动态仿真动画 - 连续播放、交替工作、完整序列
    % 4. 输出动画视频文件
    
    fprintf('=== 增强版 YuMi LEGO 城堡堆叠仿真系统 ===\n\n');
    
    %% 1. 初始化仿真参数
    fprintf('1. 初始化仿真参数...\n');
    
    % 时间参数
    dt = 0.05;  % 时间步长 (50ms) - 更细致的动画
    t_total = 60;  % 总仿真时间 (60秒)
    time_steps = 0:dt:t_total;
    n_steps = length(time_steps);
    
    % 物理参数
    gravity = [0, 0, -9.81];
    friction_coeff = 0.7;
    restitution = 0.3;
    
    % 动画参数
    animation_speed = 1.0;  % 动画播放速度倍数
    frame_rate = 20;        % 目标帧率 (fps)
    
    fprintf('✓ 仿真参数设置完成\n');
    fprintf('  - 时间步长: %.3f s\n', dt);
    fprintf('  - 总仿真时间: %.1f s\n', t_total);
    fprintf('  - 目标帧率: %d fps\n', frame_rate);
    
    %% 2. 加载机器人环境（修正坐标系）
    fprintf('\n2. 加载机器人环境（坐标系修正）...\n');
    
    try
        [yumi, qHome, table, ax] = setupRobotEnv_fixed();
        
        % 坐标系修正：旋转90度
        % 原始坐标系：X轴向前，Y轴向左
        % 修正后：X轴向右，Y轴向前（更符合直觉）
        coord_rotation = [0 1 0; -1 0 0; 0 0 1];  % 绕Z轴逆时针旋转90度
        
        fprintf('✓ 机器人环境加载成功\n');
        fprintf('✓ 坐标系已修正（旋转90度）\n');
    catch ME
        fprintf('✗ 机器人环境加载失败: %s\n', ME.message);
        return;
    end
    
    %% 3. 初始化增强版LEGO积木
    fprintf('\n3. 初始化增强版LEGO积木...\n');
    
    % LEGO积木配置（增强版）
    lego_blocks = struct();
    lego_blocks.count = 12;
    
    % 增大积木尺寸（原尺寸的2倍，更清晰可见）
    lego_blocks.size = [0.0636, 0.0318, 0.0400];  % 2x4 LEGO标准尺寸的2倍
    lego_blocks.mass = 0.004;  % 相应增加质量
    
    % 积木颜色配置（区分左右臂）
    lego_blocks.colors = [
        1.0, 0.2, 0.2;  % 红色 - 左臂积木 1
        1.0, 0.2, 0.2;  % 红色 - 左臂积木 2
        1.0, 0.2, 0.2;  % 红色 - 左臂积木 3
        1.0, 0.2, 0.2;  % 红色 - 左臂积木 4
        1.0, 0.2, 0.2;  % 红色 - 左臂积木 5
        1.0, 0.2, 0.2;  % 红色 - 左臂积木 6
        0.2, 0.2, 1.0;  % 蓝色 - 右臂积木 7
        0.2, 0.2, 1.0;  % 蓝色 - 右臂积木 8
        0.2, 0.2, 1.0;  % 蓝色 - 右臂积木 9
        0.2, 0.2, 1.0;  % 蓝色 - 右臂积木 10
        0.2, 0.2, 1.0;  % 蓝色 - 右臂积木 11
        0.2, 0.2, 1.0   % 蓝色 - 右臂积木 12
    ];
    
    % 初始化位置数组
    lego_blocks.positions = zeros(lego_blocks.count, 3, n_steps);
    lego_blocks.velocities = zeros(lego_blocks.count, 3, n_steps);
    lego_blocks.orientations = zeros(lego_blocks.count, 3, n_steps);
    lego_blocks.states = strings(lego_blocks.count, n_steps);
    
    % 修正后的初始位置（坐标系旋转90度）
    % 左臂积木：放置在机器人左侧（Y轴正方向）
    for i = 1:6
        pos_original = [-0.1 + (i-1)*0.04, 0.2, 0.07];  % 原始位置
        pos_rotated = (coord_rotation * pos_original')';  % 应用旋转
        lego_blocks.positions(i, :, 1) = pos_rotated;
        lego_blocks.states(i, 1) = "free";
    end
    
    % 右臂积木：放置在机器人右侧（Y轴负方向）
    for i = 7:12
        pos_original = [-0.1 + (i-7)*0.04, -0.2, 0.07];  % 原始位置
        pos_rotated = (coord_rotation * pos_original')';  % 应用旋转
        lego_blocks.positions(i, :, 1) = pos_rotated;
        lego_blocks.states(i, 1) = "free";
    end
    
    fprintf('✓ 增强版LEGO积木初始化完成\n');
    fprintf('  - 积木数量: %d\n', lego_blocks.count);
    fprintf('  - 增强尺寸: [%.4f, %.4f, %.4f] m\n', lego_blocks.size);
    fprintf('  - 颜色配置: 红色(左臂) + 蓝色(右臂)\n');
    
    %% 4. 定义修正后的目标位置
    fprintf('\n4. 定义修正后的目标位置...\n');
    
    % 原始目标位置（城堡第一层布局）
    target_positions_original = [
        0.0000, 0.4125, 0.0648;  % B01 -> Target01 (修正：交换X,Y)
        0.0000, 0.5875, 0.0648;  % B02 -> Target12
        -0.0080, 0.4364, 0.0648; % B03 -> Target02
        -0.0080, 0.5636, 0.0648; % B04 -> Target10
        0.0080, 0.4364, 0.0648;  % B05 -> Target03
        0.0080, 0.5636, 0.0648;  % B06 -> Target11
        -0.0080, 0.4682, 0.0648; % B07 -> Target04
        -0.0080, 0.5318, 0.0648; % B08 -> Target08
        0.0080, 0.4682, 0.0648;  % B09 -> Target05
        0.0080, 0.5318, 0.0648;  % B10 -> Target09
        -0.0080, 0.5000, 0.0648; % B11 -> Target06
        0.0080, 0.5000, 0.0648   % B12 -> Target07
    ];
    
    % 应用坐标系旋转
    target_positions = zeros(size(target_positions_original));
    for i = 1:size(target_positions_original, 1)
        target_positions(i, :) = (coord_rotation * target_positions_original(i, :)')';
    end
    
    fprintf('✓ 目标位置定义完成（已应用坐标系修正）\n');
    fprintf('  - 目标位置数量: %d\n', size(target_positions, 1));
    
    %% 5. 创建增强版任务序列（交替执行）
    fprintf('\n5. 创建增强版任务序列（交替执行）...\n');
    
    task_sequence = struct();
    task_sequence.current_task = 1;
    task_sequence.tasks = [];
    
    % 创建交替执行的任务序列
    task_order = [1, 7, 2, 8, 3, 9, 4, 10, 5, 11, 6, 12];  % 左右臂交替
    
    for idx = 1:length(task_order)
        i = task_order(idx);
        task = struct();
        task.block_id = i;
        if i <= 6
            task.arm = "left";
        else
            task.arm = "right";
        end
        task.pick_pos = lego_blocks.positions(i, :, 1);
        task.place_pos = target_positions(i, :);
        task.status = "pending";
        task.start_time = (idx-1) * 5.0;  % 每个任务5秒
        task.duration = 4.0;  % 4秒完成动作
        task.color = lego_blocks.colors(i, :);
        
        task_sequence.tasks = [task_sequence.tasks; task];
    end
    
    fprintf('✓ 增强版任务序列创建完成\n');
    fprintf('  - 任务总数: %d\n', length(task_sequence.tasks));
    fprintf('  - 执行模式: 左右臂交替\n');
    fprintf('  - 任务顺序: ');
    for i = 1:length(task_order)
        fprintf('B%02d(%s) ', task_order(i), task_sequence.tasks(i).arm);
    end
    fprintf('\n');
    
    %% 6. 初始化机器人状态
    fprintf('\n6. 初始化机器人状态...\n');
    
    robot_states = struct();
    robot_states.q_left = zeros(7, n_steps);
    robot_states.q_right = zeros(7, n_steps);
    robot_states.qd_left = zeros(7, n_steps);
    robot_states.qd_right = zeros(7, n_steps);
    robot_states.gripper_left = zeros(1, n_steps);
    robot_states.gripper_right = zeros(1, n_steps);
    robot_states.ee_pos_left = zeros(3, n_steps);
    robot_states.ee_pos_right = zeros(3, n_steps);
    
    % 设置初始配置
    robot_states.q_left(:, 1) = qHome(1:7)';
    robot_states.q_right(:, 1) = qHome(8:14)';
    robot_states.gripper_left(1) = 0;
    robot_states.gripper_right(1) = 0;
    
    % 计算初始末端执行器位置
    T_left = getTransform(yumi, qHome, 'gripper_l_base');
    T_right = getTransform(yumi, qHome, 'gripper_r_base');
    robot_states.ee_pos_left(:, 1) = T_left(1:3, 4);
    robot_states.ee_pos_right(:, 1) = T_right(1:3, 4);
    
    fprintf('✓ 机器人状态初始化完成\n');
    
    %% 7. 准备动画录制
    fprintf('\n7. 准备动画录制...\n');
    
    % 创建图形窗口
    fig = figure('Name', '增强版 YuMi LEGO 城堡堆叠仿真', 'Position', [100, 100, 1400, 900]);
    clf;
    
    % 重新设置机器人环境
    [yumi, qHome, table, ax] = setupRobotEnv_fixed();
    hold on;
    
    % 设置视角
    view(45, 30);
    axis equal;
    grid on;
    
    % 准备视频录制
    video_filename = 'enhanced_yumi_lego_animation.avi';
    video_writer = VideoWriter(video_filename);
    video_writer.FrameRate = frame_rate;
    open(video_writer);
    
    fprintf('✓ 动画录制准备完成\n');
    fprintf('  - 视频文件: %s\n', video_filename);
    fprintf('  - 帧率: %d fps\n', frame_rate);
    
    fprintf('\n=== 开始增强版仿真动画 ===\n');

    %% 8. 主仿真循环
    frame_count = 0;
    last_frame_time = 0;
    frame_interval = 1 / frame_rate;

    % 初始化积木图形句柄
    block_handles = [];
    for i = 1:lego_blocks.count
        % 创建增强版积木可视化
        [X, Y, Z] = create_enhanced_lego_block(lego_blocks.size);
        pos = lego_blocks.positions(i, :, 1);

        % 绘制积木
        h = surf(X + pos(1), Y + pos(2), Z + pos(3), ...
                'FaceColor', lego_blocks.colors(i, :), ...
                'EdgeColor', 'k', 'LineWidth', 1.5, ...
                'FaceAlpha', 0.8);
        block_handles = [block_handles; h];

        % 添加积木标签
        text(pos(1), pos(2), pos(3) + lego_blocks.size(3)/2 + 0.01, ...
             sprintf('B%02d', i), 'HorizontalAlignment', 'center', ...
             'FontSize', 10, 'FontWeight', 'bold', 'Color', 'k');
    end

    % 添加状态显示文本
    status_text = text(0.1, 0.1, 0.3, '', 'FontSize', 12, 'FontWeight', 'bold', ...
                      'BackgroundColor', 'white', 'EdgeColor', 'black');

    for step = 2:n_steps
        current_time = time_steps(step);

        % 更新任务状态
        current_task_info = '';
        active_tasks = [];

        for i = 1:length(task_sequence.tasks)
            task = task_sequence.tasks(i);

            if strcmp(task.status, "pending") && current_time >= task.start_time
                task_sequence.tasks(i).status = "active";
                fprintf('  [%.1fs] 开始任务: %s臂拾取积木B%02d\n', current_time, task.arm, task.block_id);
            elseif strcmp(task.status, "active") && current_time >= (task.start_time + task.duration)
                task_sequence.tasks(i).status = "completed";
                fprintf('  [%.1fs] 完成任务: 积木B%02d已放置\n', current_time, task.block_id);
            end

            if strcmp(task.status, "active")
                active_tasks = [active_tasks; task];
                current_task_info = sprintf('执行中: %s臂 → 积木B%02d', upper(task.arm), task.block_id);
            end
        end

        % 更新机器人状态（简化）
        robot_states.q_left(:, step) = robot_states.q_left(:, step-1);
        robot_states.q_right(:, step) = robot_states.q_right(:, step-1);
        robot_states.ee_pos_left(:, step) = robot_states.ee_pos_left(:, step-1);
        robot_states.ee_pos_right(:, step) = robot_states.ee_pos_right(:, step-1);

        % 更新LEGO积木位置和状态
        for i = 1:lego_blocks.count
            % 复制前一步状态
            lego_blocks.positions(i, :, step) = lego_blocks.positions(i, :, step-1);
            lego_blocks.velocities(i, :, step) = lego_blocks.velocities(i, :, step-1);
            lego_blocks.orientations(i, :, step) = lego_blocks.orientations(i, :, step-1);
            lego_blocks.states(i, step) = lego_blocks.states(i, step-1);

            % 检查是否有任务正在操作此积木
            task_idx = find([task_sequence.tasks.block_id] == i);
            if ~isempty(task_idx)
                task = task_sequence.tasks(task_idx);
                if strcmp(task.status, "active")
                    % 计算任务进度
                    task_progress = (current_time - task.start_time) / task.duration;
                    task_progress = max(0, min(1, task_progress));

                    % 创建平滑的运动轨迹（包含拾取高度）
                    start_pos = task.pick_pos;
                    end_pos = task.place_pos;

                    % 添加中间拾取点（提升高度）
                    if task_progress <= 0.3
                        % 阶段1：移动到拾取位置上方
                        pickup_height = start_pos + [0, 0, 0.1];
                        current_pos = start_pos + task_progress/0.3 * (pickup_height - start_pos);
                    elseif task_progress <= 0.5
                        % 阶段2：下降到拾取位置
                        pickup_height = start_pos + [0, 0, 0.1];
                        progress_local = (task_progress - 0.3) / 0.2;
                        current_pos = pickup_height + progress_local * (start_pos - pickup_height);
                    elseif task_progress <= 0.7
                        % 阶段3：提升到运输高度
                        transport_height = start_pos + [0, 0, 0.15];
                        progress_local = (task_progress - 0.5) / 0.2;
                        current_pos = start_pos + progress_local * (transport_height - start_pos);
                    else
                        % 阶段4：移动到目标位置
                        transport_height = start_pos + [0, 0, 0.15];
                        target_height = end_pos + [0, 0, 0.05];
                        progress_local = (task_progress - 0.7) / 0.3;

                        if progress_local <= 0.8
                            % 水平移动到目标上方
                            current_pos = transport_height + (progress_local/0.8) * ...
                                         (target_height - transport_height);
                        else
                            % 下降到目标位置
                            progress_final = (progress_local - 0.8) / 0.2;
                            current_pos = target_height + progress_final * (end_pos - target_height);
                        end
                    end

                    lego_blocks.positions(i, :, step) = current_pos;
                    lego_blocks.states(i, step) = "moving";

                elseif strcmp(task.status, "completed")
                    lego_blocks.positions(i, :, step) = task.place_pos;
                    lego_blocks.states(i, step) = "placed";
                end
            end
        end

        % 应用物理效果（重力）
        for i = 1:lego_blocks.count
            if lego_blocks.states(i, step) == "free"
                % 应用重力
                lego_blocks.velocities(i, 3, step) = lego_blocks.velocities(i, 3, step-1) + gravity(3) * dt;

                % 更新位置
                lego_blocks.positions(i, :, step) = lego_blocks.positions(i, :, step-1) + ...
                    lego_blocks.velocities(i, :, step) * dt;

                % 地面碰撞检测
                if lego_blocks.positions(i, 3, step) <= 0.06
                    lego_blocks.positions(i, 3, step) = 0.06;
                    lego_blocks.velocities(i, 3, step) = 0;
                end
            end
        end

        % 动画帧更新
        if current_time - last_frame_time >= frame_interval
            % 更新积木位置
            for i = 1:lego_blocks.count
                pos = lego_blocks.positions(i, :, step);
                [X, Y, Z] = create_enhanced_lego_block(lego_blocks.size);

                % 更新积木图形
                set(block_handles(i), 'XData', X + pos(1), ...
                                     'YData', Y + pos(2), ...
                                     'ZData', Z + pos(3));
            end

            % 更新状态文本
            if ~isempty(current_task_info)
                set(status_text, 'String', sprintf('时间: %.1fs | %s', current_time, current_task_info));
            else
                set(status_text, 'String', sprintf('时间: %.1fs | 等待下一个任务...', current_time));
            end

            % 更新标题
            completed_count = sum([task_sequence.tasks.status] == "completed");
            title(sprintf('增强版 YuMi LEGO 城堡堆叠仿真 | 已完成: %d/%d', ...
                         completed_count, length(task_sequence.tasks)), ...
                  'FontSize', 14, 'FontWeight', 'bold');

            % 刷新显示
            drawnow;

            % 录制帧
            frame = getframe(fig);
            writeVideo(video_writer, frame);

            frame_count = frame_count + 1;
            last_frame_time = current_time;
        end

        % 进度指示
        if mod(step, round(n_steps/20)) == 0
            progress = step / n_steps * 100;
            fprintf('  仿真进度: %.1f%% (帧数: %d)\n', progress, frame_count);
        end
    end

    % 关闭视频录制
    close(video_writer);

    fprintf('✓ 动画录制完成\n');
    fprintf('  - 总帧数: %d\n', frame_count);
    fprintf('  - 视频文件: %s\n', video_filename);

    %% 9. 生成仿真结果
    fprintf('\n9. 生成增强版仿真结果...\n');

    sim_results = struct();
    sim_results.time = time_steps;
    sim_results.robot_states = robot_states;
    sim_results.lego_blocks = lego_blocks;
    sim_results.task_sequence = task_sequence;
    sim_results.target_positions = target_positions;
    sim_results.coord_rotation = coord_rotation;
    sim_results.animation_info = struct('frame_count', frame_count, ...
                                       'video_file', video_filename, ...
                                       'frame_rate', frame_rate);

    % 计算最终精度
    final_positions = lego_blocks.positions(:, :, end);
    position_errors = zeros(lego_blocks.count, 1);

    for i = 1:lego_blocks.count
        error = norm(final_positions(i, :) - target_positions(i, :));
        position_errors(i) = error;
    end

    sim_results.position_errors = position_errors;
    sim_results.mean_error = mean(position_errors);
    sim_results.max_error = max(position_errors);

    % 保存结果
    save('enhanced_simulation_results.mat', 'sim_results');

    % 保存最终状态图片
    saveas(fig, 'enhanced_simulation_final.png');

    fprintf('✓ 增强版仿真结果生成完成\n');
    fprintf('  - 平均位置误差: %.4f m\n', sim_results.mean_error);
    fprintf('  - 最大位置误差: %.4f m\n', sim_results.max_error);
    fprintf('  - 完成任务数: %d/%d\n', sum([task_sequence.tasks.status] == "completed"), length(task_sequence.tasks));

    %% 10. 总结报告
    fprintf('\n=== 增强版仿真完成总结 ===\n');
    fprintf('仿真类型: 增强版 YuMi LEGO 城堡堆叠\n');
    fprintf('主要改进:\n');
    fprintf('  ✓ 坐标系修正 (旋转90度)\n');
    fprintf('  ✓ 积木可视化增强 (2倍尺寸 + 颜色区分)\n');
    fprintf('  ✓ 动态动画实现 (交替执行 + 完整序列)\n');
    fprintf('  ✓ 视频输出功能 (%s)\n', video_filename);
    fprintf('仿真时间: %.1f 秒\n', t_total);
    fprintf('积木数量: %d (红色:左臂, 蓝色:右臂)\n', lego_blocks.count);
    fprintf('任务完成率: %.1f%%\n', sum([task_sequence.tasks.status] == "completed") / length(task_sequence.tasks) * 100);
    fprintf('平均精度: %.2f mm\n', sim_results.mean_error * 1000);
    fprintf('动画帧数: %d 帧 @ %d fps\n', frame_count, frame_rate);
    fprintf('状态: 增强版仿真成功完成！\n');
    fprintf('=====================================\n');

end

function [X, Y, Z] = create_enhanced_lego_block(size)
    % 创建增强版LEGO积木的3D模型
    % 输入: size - [长度, 宽度, 高度]
    % 输出: X, Y, Z - 3D网格坐标

    % 创建基本长方体
    [X, Y, Z] = meshgrid([-size(1)/2, size(1)/2], ...
                         [-size(2)/2, size(2)/2], ...
                         [0, size(3)]);

    % 重新整理为surf函数需要的格式
    X = [X(:,:,1), X(:,:,2); X(:,:,1), X(:,:,2)];
    Y = [Y(:,:,1), Y(:,:,2); Y(:,:,1), Y(:,:,2)];
    Z = [Z(:,:,1), Z(:,:,1); Z(:,:,2), Z(:,:,2)];
end
