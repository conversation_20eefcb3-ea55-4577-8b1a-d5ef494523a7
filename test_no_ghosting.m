function test_no_ghosting()
    % 测试重影修复效果的简化脚本
    % 运行短时间动画来验证是否还有重影问题
    
    fprintf('=== 重影修复测试 ===\n\n');
    
    %% 1. 检查文件存在
    if ~exist('optimized_yumi_animation_fixed.m', 'file')
        fprintf('❌ 未找到修复版文件 optimized_yumi_animation_fixed.m\n');
        return;
    end
    
    if ~exist('積木座標.csv(1).xlsx', 'file')
        fprintf('❌ 未找到数据文件 積木座標.csv(1).xlsx\n');
        return;
    end
    
    fprintf('✓ 所有必要文件存在\n');
    
    %% 2. 创建测试版本（短时间运行）
    fprintf('\n2. 创建测试版本...\n');
    create_test_version_fixed();
    fprintf('✓ 测试版本创建完成\n');
    
    %% 3. 运行测试
    fprintf('\n3. 运行重影修复测试（30秒）...\n');
    fprintf('请观察动画是否有重影现象...\n\n');
    
    try
        tic;
        test_results = optimized_yumi_animation_test_fixed();
        test_time = toc;
        
        fprintf('\n✅ 测试完成！\n');
        fprintf('测试时间: %.2f秒\n', test_time);
        fprintf('总帧数: %d\n', test_results.frame_count);
        fprintf('平均FPS: %.1f\n', test_results.frame_count / test_time);
        
        % 检查是否有重影相关的错误
        if isfield(test_results, 'errors') && ~isempty(test_results.errors)
            fprintf('⚠️  发现以下问题:\n');
            for i = 1:length(test_results.errors)
                fprintf('   - %s\n', test_results.errors{i});
            end
        else
            fprintf('✅ 未发现重影或其他图形问题\n');
        end
        
    catch ME
        fprintf('❌ 测试失败: %s\n', ME.message);
        fprintf('错误位置: %s (第%d行)\n', ME.stack(1).file, ME.stack(1).line);
    end
    
    %% 4. 清理测试文件
    if exist('optimized_yumi_animation_test_fixed.m', 'file')
        delete('optimized_yumi_animation_test_fixed.m');
        fprintf('\n🧹 测试文件已清理\n');
    end
    
    %% 5. 重影检查指南
    fprintf('\n=== 重影检查指南 ===\n');
    fprintf('如果您在测试中观察到以下现象，说明重影问题已解决:\n');
    fprintf('✅ 机器人模型清晰，没有重叠的半透明图像\n');
    fprintf('✅ 积木移动时轨迹清晰，没有残影\n');
    fprintf('✅ 图形更新流畅，没有闪烁\n');
    fprintf('✅ 视频录制画面清晰，没有重影\n\n');
    
    fprintf('如果仍有重影问题，请检查:\n');
    fprintf('🔧 显卡驱动是否最新\n');
    fprintf('🔧 MATLAB图形设置是否正确\n');
    fprintf('🔧 是否有其他图形程序占用资源\n');
    
    fprintf('\n✅ 重影修复测试完成！\n');
end

function create_test_version_fixed()
    % 创建用于测试的短时间版本
    
    % 读取修复版文件
    content = fileread('optimized_yumi_animation_fixed.m');
    
    % 替换函数名和时间参数
    content = strrep(content, 'function [sim_results] = optimized_yumi_animation_fixed()', ...
                     'function [sim_results] = optimized_yumi_animation_test_fixed()');
    content = strrep(content, 't_total = 120;', 't_total = 30;');  % 30秒测试
    content = strrep(content, 'video_filename = ''yumi_lego_fixed.mp4'';', ...
                     'video_filename = ''test_no_ghosting.mp4'';');
    
    % 写入测试文件
    fid = fopen('optimized_yumi_animation_test_fixed.m', 'w');
    fprintf(fid, '%s', content);
    fclose(fid);
end

function show_ghosting_comparison()
    % 显示重影问题对比说明
    
    fprintf('\n=== 重影问题对比说明 ===\n\n');
    
    fprintf('🔴 原版问题 (enhanced_yumi_animation.m):\n');
    fprintf('   - 机器人模型重复渲染，产生重影\n');
    fprintf('   - PreservePlot=false 设置不当\n');
    fprintf('   - 每帧完全重绘，效率低下\n');
    fprintf('   - 图形对象管理混乱\n\n');
    
    fprintf('🟡 第一次优化问题 (optimized_yumi_animation.m):\n');
    fprintf('   - 提高了性能但引入了重影\n');
    fprintf('   - PreservePlot=true 导致图形累积\n');
    fprintf('   - 机器人更新逻辑有误\n\n');
    
    fprintf('🟢 修复版解决方案 (optimized_yumi_animation_fixed.m):\n');
    fprintf('   - 正确管理机器人图形对象生命周期\n');
    fprintf('   - 使用delete()明确删除旧对象\n');
    fprintf('   - 优化帧跳过策略，减少不必要更新\n');
    fprintf('   - 设置正确的坐标轴属性\n');
    fprintf('   - 保持高性能的同时消除重影\n\n');
    
    fprintf('🔧 关键修复技术:\n');
    fprintf('   1. 显式删除机器人图形对象: delete(robot_handles)\n');
    fprintf('   2. 正确的PreservePlot设置: PreservePlot=false\n');
    fprintf('   3. 坐标轴NextPlot设置: set(ax, ''NextPlot'', ''add'')\n');
    fprintf('   4. 智能更新频率控制\n');
    fprintf('   5. 图形对象引用管理\n\n');
end

function performance_tips()
    % 性能优化提示
    
    fprintf('=== 性能优化提示 ===\n\n');
    
    fprintf('🚀 如果动画仍然较慢，可以尝试:\n\n');
    
    fprintf('1. 调整帧跳过频率:\n');
    fprintf('   - 在 update_animation_fixed 中修改: frame_skip_counter >= 5\n');
    fprintf('   - 增加数字可提高性能，但会降低流畅度\n\n');
    
    fprintf('2. 降低视频质量:\n');
    fprintf('   - 修改: video_writer.Quality = 70; (默认90)\n');
    fprintf('   - 可显著提高录制速度\n\n');
    
    fprintf('3. 减少积木数量:\n');
    fprintf('   - 在数据文件中选择部分积木进行测试\n');
    fprintf('   - 验证算法正确性后再使用完整数据\n\n');
    
    fprintf('4. 优化图形设置:\n');
    fprintf('   - 关闭不必要的光照效果\n');
    fprintf('   - 使用更简单的积木模型\n');
    fprintf('   - 调整渲染器设置\n\n');
    
    fprintf('5. 硬件优化:\n');
    fprintf('   - 确保使用独立显卡\n');
    fprintf('   - 关闭其他占用GPU的程序\n');
    fprintf('   - 增加MATLAB内存分配\n\n');
end

function troubleshooting_guide()
    % 故障排除指南
    
    fprintf('=== 故障排除指南 ===\n\n');
    
    fprintf('❌ 如果仍有重影问题:\n');
    fprintf('   1. 检查MATLAB版本 (推荐R2020b或更新)\n');
    fprintf('   2. 更新显卡驱动\n');
    fprintf('   3. 重启MATLAB清除图形缓存\n');
    fprintf('   4. 检查是否有多个figure窗口\n\n');
    
    fprintf('❌ 如果性能仍然很慢:\n');
    fprintf('   1. 运行 quick_performance_test() 诊断瓶颈\n');
    fprintf('   2. 检查系统资源使用情况\n');
    fprintf('   3. 尝试降低动画复杂度\n');
    fprintf('   4. 考虑使用更强大的硬件\n\n');
    
    fprintf('❌ 如果出现错误:\n');
    fprintf('   1. 检查所有必要文件是否存在\n');
    fprintf('   2. 验证数据文件格式是否正确\n');
    fprintf('   3. 确保setupRobotEnv_fixed()函数可用\n');
    fprintf('   4. 检查MATLAB工具箱依赖\n\n');
    
    fprintf('💡 获取帮助:\n');
    fprintf('   - 运行测试脚本获取详细诊断信息\n');
    fprintf('   - 查看生成的错误日志\n');
    fprintf('   - 检查MATLAB命令窗口的警告信息\n\n');
end

% 在脚本末尾添加额外的帮助信息
if nargout == 0
    fprintf('\n=== 额外帮助信息 ===\n');
    fprintf('运行以下命令获取更多信息:\n');
    fprintf('>> show_ghosting_comparison()  %% 查看重影问题对比\n');
    fprintf('>> performance_tips()          %% 获取性能优化提示\n');
    fprintf('>> troubleshooting_guide()     %% 查看故障排除指南\n');
end
