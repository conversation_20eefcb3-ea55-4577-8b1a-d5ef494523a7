function setup_lego_and_run_sim()
    %% 项目仿真主脚本
    % 该脚本负责：
    % 1. 清理并设置Simulink模型。
    % 2. 创建或更新LEGO积木模板。
    % 3. 读取LEGO积木坐标数据。
    % 4. 在Simulink模型中动态创建所有LEGO积木。
    % 5. 规划双臂轨迹 (TODO)。
    % 6. 运行仿真 (TODO)。

    % --- 1. 初始化和模型设置 ---
    model = 'Yumi_Lego_Final_Simulation';
    
    % 在开始前，确保模型是加载的
    if ~bdIsLoaded(model)
        load_system(model);
    end
    
    fprintf('模型 %s 已加载。\n', model);

    % --- 2. 清理旧的积木并创建模板 ---
    fprintf('正在清理旧的积木并创建模板...\n');
    
    % 清理之前运行生成的积木
    all_blocks = find_system(model, 'SearchDepth', 1, 'RegExp', 'on', 'Name', 'LegoBrick_\d+');
    for i = 1:length(all_blocks)
        delete_block(all_blocks{i});
    end
    fprintf('已清理 %d 个旧积木。\n', length(all_blocks));
    
    % 定义可复用LEGO积木子系统的路径并创建它
    lego_subsystem_path = [model '/LegoBrickTemplate'];
    create_lego_template(model, lego_subsystem_path);

    % --- 3. 读取LEGO积木坐标数据 ---
    % !! 关键假设 !!
    % - 我们假设Excel文件名是 '積木座標.csv.xlsx'
    % - 我们假设工作表名是 'Sheet1'
    % - 我们假设坐标列名是 'X', 'Y', 'Z'
    % 如果这些假设不正确，请您在脚本的这一部分进行修改。
    fprintf('正在读取LEGO坐标...\n');
    try
        lego_coords_table = readtable('说明/積木座標.csv.xlsx', 'Sheet', 'Sheet1');
        fprintf('成功读取 %d 个积木坐标。\n', height(lego_coords_table));
    catch ME
        error('无法读取 "說明/積木座標.csv.xlsx"。请检查：1.文件名是否正确 2.工作表名是否为"Sheet1" 3.文件是否在"说明"文件夹下。错误: %s', ME.message);
    end

    % --- 4. 在Simulink中动态创建LEGO积木 ---
    fprintf('开始批量创建LEGO积木...\n');
    
    for i = 1:height(lego_coords_table)
        brick_data = lego_coords_table(i, :);
        
        try
            initial_pos = [brick_data.X, brick_data.Y, brick_data.Z]; 
        catch
            error('无法从Excel中读取 X, Y, Z 列。请检查您的Excel文件，确保坐标列的表头是 "X", "Y", "Z"。');
        end
        
        new_brick_name = ['LegoBrick_' num2str(i)];
        new_brick_path = [model '/' new_brick_name];
        
        add_block(lego_subsystem_path, new_brick_path, 'MakeNameUnique', 'on');
        
        transform_block_path = [new_brick_path '/InitialTransform'];
        % 坐标单位是米。根据积木高度(约0.0096m)，我们将Z坐标增加一半高度，使其底部与Z=0的工作台表面接触
        set_param(transform_block_path, 'Translation', ['[', num2str(initial_pos + [0, 0, 0.0048]), ']']);
        
        % 将新积木连接到世界坐标系
        add_line(model, 'World Frame/Rconn1', [new_brick_name '/B'], 'autorouting', 'on');
        
        fprintf('已创建积木: %s\n', new_brick_name);
    end
    
    fprintf('所有积木已在模型中创建完毕。\n');
    
    % 保存模型
    save_system(model);
    fprintf('模型已保存。\n');

end

function create_lego_template(model, subsystem_path)
    %% 创建一个强大的、可复用的LEGO积木模板子系统
    fprintf('正在创建或更新LEGO模板...\n');
    
    % 确保子系统是全新的
    try
        delete_block(subsystem_path);
    catch
        % 如果不存在，则忽略错误，继续执行
    end
    add_block('simulink/Ports & Subsystems/Subsystem', subsystem_path);
    
    % 清空子系统内部的默认模块
    Simulink.SubSystem.deleteContents(subsystem_path);

    % 定义内部模块的路径
    port_path = [subsystem_path '/B'];
    transform_path = [subsystem_path '/InitialTransform'];
    joint_path = [subsystem_path '/FreeJoint'];
    body_path = [subsystem_path '/LegoBody'];

    % 在子系统内部添加所有必需的模块
    add_block('nesl_utility/Connection Port', port_path, 'Side', 'Left');
    add_block('sm_lib/Frames and Transforms/Rigid Transform', transform_path);
    add_block('sm_lib/Joints/6-DOF Joint', joint_path);
    add_block('sm_lib/Body Elements/File Solid', body_path);
    
    % 使用我们下载的STL文件配置积木实体
    set_param(body_path, 'FileName', 'meshes/LEGO-2X3-S.stl');
    
    % 自动连接内部模块
    add_line(subsystem_path, 'B/Rconn1', 'InitialTransform/Lconn1', 'autorouting', 'on');
    add_line(subsystem_path, 'InitialTransform/Rconn1', 'FreeJoint/Lconn1', 'autorouting', 'on');
    add_line(subsystem_path, 'FreeJoint/Rconn1', 'LegoBody/Rconn1', 'autorouting', 'on');

    fprintf('LEGO模板已成功创建并配置。\n');
end 