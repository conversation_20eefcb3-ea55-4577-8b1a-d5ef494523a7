# YuMi 双臂机器人 LEGO 城堡堆叠仿真项目 - 详细任务书

## 📋 项目概述

### 项目目标
基于 MATLAB/Simulink 平台，实现 ABB YuMi 双臂协作机器人精确搭建 8 层 LEGO 城堡的完整仿真系统。项目将展示双臂机器人在复杂装配任务中的协作能力，并提供详细的仿真数据用于学术研究和工程分析。

### 核心技术要求
- **仿真平台**: MATLAB R2023a+ & Simulink with Simscape Multibody
- **机器人模型**: ABB YuMi 双臂协作机器人 (URDF格式)
- **参考教程**: [MathWorks 官方教程](https://uk.mathworks.com/help/robotics/ug/model-and-control-a-manipulator-arm-with-simscape.html)
- **目标结构**: 与 `main building.ldr` 设计完全一致的 8 层城堡
- **精度要求**: 积木位置误差 < 1mm，角度误差 < 1°

### 成功标准
1. **功能完整性**: Simulink 中完整展示从夹取到堆叠的全流程
2. **动作准确性**: 双臂运动轨迹流畅，夹爪开合时机精确
3. **结果一致性**: 最终城堡结构与设计图纸完全匹配
4. **数据可追溯**: 输出关节角度、力矩、接触力等完整数据

---

## 🗂️ 项目现状分析

### 已有代码资源
- ✅ **setupRobotEnv.m**: YuMi 机器人模型加载和环境配置
- ✅ **planTrajectory.m**: 基于逆运动学的轨迹规划算法
- ✅ **lego_config.m**: LEGO 积木配置和任务序列定义
- ✅ **runSimulink.m**: Simulink 仿真执行和数据格式转换
- ✅ **setup_lego_and_run_sim.m**: 主仿真脚本和 LEGO 模板创建
- ✅ **YumiSimscape.slx**: 基础 Simulink 仿真模型

### 数据资源
- ✅ **积木座标.csv(1).xlsx**: 完整的积木坐标数据
- ✅ **第一層積木.csv.xlsx**: 第一层详细布局数据
- ✅ **城堡设计图片**: 8 层城堡的可视化参考
- ✅ **STL 文件**: LEGO 积木的 3D 模型文件

### 核心技术挑战
1. **数据接口问题**: MATLAB 轨迹数据与 Simulink 模块的格式匹配
2. **坐标系不一致**: MATLAB 和 Simulink 之间的坐标系统一
3. **夹爪控制逻辑**: 精确的开合时机和力度控制
4. **双臂协调**: 避障和任务分配优化
5. **物理仿真**: LEGO 积木的真实碰撞和堆叠效果

---

## 🎯 详细实施计划

### 阶段一：仿真环境搭建与模型优化 (3-4天)

#### 任务 1.1: YuMi 机器人模型验证与优化
**目标**: 确保 YuMi 机器人在 Simulink 中正确显示和运行

**具体步骤**:
1. 验证 `abbYumi_exported.urdf` 文件完整性
2. 使用 `smimport('abbYumi_exported.urdf')` 重新生成 Simscape 模型
3. 检查所有关节和连杆的物理属性设置
4. 验证双臂的运动范围和约束条件

**验收标准**:
- [ ] YuMi 模型在 Simulink 中正确显示
- [ ] 所有 14 个关节 (每臂 7 个) 可独立控制
- [ ] 夹爪模型正确加载并可视化

#### 任务 1.2: 建立统一坐标系统
**目标**: 解决 MATLAB 与 Simulink 坐标系不一致问题

**具体步骤**:
1. 在 Simulink 模型中添加 `World Frame` 作为全局参考
2. 使用 `Rigid Transform` 模块建立 World Frame 到 YuMi 基座的固定变换
3. 修改 `lego_config.m` 中的坐标定义，统一使用 World Frame
4. 验证积木初始位置和目标位置的坐标一致性

**验收标准**:
- [ ] 建立唯一的 World Frame 参考系
- [ ] MATLAB 和 Simulink 中积木位置完全一致
- [ ] 坐标变换矩阵正确配置

#### 任务 1.3: Simscape 物理环境配置
**目标**: 配置真实的物理仿真环境

**具体步骤**:
1. 添加 `Mechanism Configuration` 模块，设置重力 `[0 0 -9.81]`
2. 配置 `Solver Configuration` 模块，优化求解器参数
3. 设置仿真时间步长和求解精度
4. 添加工作台和环境边界

**验收标准**:
- [ ] 物理环境参数正确设置
- [ ] 重力效应正确模拟
- [ ] 仿真稳定性良好

---

### 阶段二：数据接口与轨迹传输优化 (2-3天)

#### 任务 2.1: 轨迹数据格式标准化
**目标**: 确保 MATLAB 轨迹数据正确传输到 Simulink

**具体步骤**:
1. 修改 `runSimulink.m` 中的数据格式转换逻辑
2. 采用标准 `timeseries` 格式: `[time, q1, q2, ..., q7]`
3. 为左臂和右臂分别创建独立的 timeseries 对象
4. 添加夹爪控制信号的 timeseries 数据

**验收标准**:
- [ ] 轨迹数据格式符合 Simulink 要求
- [ ] 左右臂数据正确分离
- [ ] 夹爪控制信号同步

#### 任务 2.2: Simulink 数据输入模块配置
**目标**: 配置 From Workspace 模块正确读取轨迹数据

**具体步骤**:
1. 在 YumiSimscape.slx 中添加 `From Workspace` 模块
2. 配置模块参数：数据格式、插值方法、外推方法
3. 添加 `Simulink-PS Converter` 进行信号转换
4. 建立从数据输入到关节驱动的完整信号链

**验收标准**:
- [ ] From Workspace 模块正确配置
- [ ] 信号转换无误差
- [ ] 关节驱动响应正确

---

### 阶段三：夹爪控制与 LEGO 物理模型 (3-4天)

#### 任务 3.1: 夹爪控制逻辑实现
**目标**: 实现精确的夹爪开合控制

**具体步骤**:
1. 分析夹爪的机械结构和控制方式
2. 实现夹爪开合的时序控制逻辑
3. 添加力反馈控制，确保夹取力度适中
4. 集成夹爪状态到主控制系统

**验收标准**:
- [ ] 夹爪开合动作流畅
- [ ] 夹取力度可控
- [ ] 时序控制精确

#### 任务 3.2: LEGO 积木物理模型集成
**目标**: 导入真实的 LEGO STL 模型并配置物理属性

**具体步骤**:
1. 优化 `create_lego_template` 函数
2. 配置 `File Solid` 模块加载 LEGO STL 文件
3. 设置积木的质量、惯量和材料属性
4. 配置碰撞检测和接触力计算

**验收标准**:
- [ ] LEGO 模型正确显示
- [ ] 物理属性设置合理
- [ ] 碰撞检测正常工作

---

### 阶段四：双臂协调与避障优化 (2-3天)

#### 任务 4.1: 双臂任务分配算法优化
**目标**: 优化双臂的任务分配和协调逻辑

**具体步骤**:
1. 分析当前 `lego_config.m` 中的任务分配逻辑
2. 基于积木位置和机器人工作空间优化分配算法
3. 实现动态任务调整机制
4. 添加负载平衡考虑

**验收标准**:
- [ ] 任务分配合理高效
- [ ] 双臂工作负载均衡
- [ ] 支持动态调整

#### 任务 4.2: 碰撞避障系统
**目标**: 确保双臂运动过程中无碰撞

**具体步骤**:
1. 实现双臂之间的碰撞检测
2. 添加与已堆叠积木的避障逻辑
3. 优化轨迹规划算法，集成避障约束
4. 实现紧急停止和恢复机制

**验收标准**:
- [ ] 双臂无碰撞风险
- [ ] 避障轨迹流畅
- [ ] 安全机制完善

---

### 阶段五：系统集成与全面验证 (3-4天)

#### 任务 5.1: 第一层积木验证
**目标**: 完成第一层 12 块积木的精确堆叠

**具体步骤**:
1. 集成所有模块进行第一层仿真
2. 验证每块积木的位置精度
3. 检查堆叠的稳定性和美观度
4. 记录详细的仿真数据

**验收标准**:
- [ ] 12 块积木全部正确放置
- [ ] 位置误差 < 1mm
- [ ] 堆叠结构稳定

#### 任务 5.2: 完整 8 层城堡仿真
**目标**: 扩展到完整的 8 层城堡搭建

**具体步骤**:
1. 扩展轨迹规划算法支持多层堆叠
2. 优化仿真性能，支持长时间运行
3. 实现分层验证和错误恢复
4. 生成完整的仿真视频

**验收标准**:
- [ ] 8 层城堡完整搭建
- [ ] 结构与设计图一致
- [ ] 仿真过程稳定

#### 任务 5.3: 数据记录与分析
**目标**: 输出完整的仿真数据用于分析

**具体步骤**:
1. 配置数据记录模块
2. 记录关节角度、速度、加速度
3. 记录接触力和力矩数据
4. 生成分析报告和可视化图表

**验收标准**:
- [ ] 数据记录完整准确
- [ ] 分析报告详细
- [ ] 可视化效果良好

---

## 📊 项目管理

### 时间规划
- **总工期**: 12-17 个工作日
- **关键里程碑**:
  - 第 4 天: 仿真环境搭建完成
  - 第 7 天: 数据接口调试完成
  - 第 11 天: 第一层积木验证通过
  - 第 15 天: 完整系统集成完成

### 风险管理
1. **技术风险**: 坐标系不一致、数据格式问题
   - **缓解措施**: 建立详细的测试用例，分步验证
2. **时间风险**: 复杂度超出预期
   - **缓解措施**: 采用敏捷开发，优先核心功能
3. **质量风险**: 仿真精度不够
   - **缓解措施**: 建立严格的验收标准

### 质量保证
- 每个任务都有明确的验收标准
- 建立单元测试和集成测试
- 详细的代码注释和文档
- 定期的技术评审

---

## 🎯 预期成果

### 技术成果
1. **完整的 Simulink 仿真模型**: 可重复运行的 YuMi LEGO 堆叠仿真
2. **优化的控制算法**: 双臂协调和轨迹规划算法
3. **详细的技术文档**: 包含设计思路、实现细节、使用说明

### 学术价值
1. **仿真数据**: 关节轨迹、力学分析、性能评估数据
2. **算法创新**: 双臂协调、避障规划的算法改进
3. **应用示范**: 协作机器人在精密装配中的应用案例

### 工程应用
1. **可扩展性**: 支持不同的积木类型和搭建任务
2. **实用性**: 可作为实际机器人控制的参考
3. **教育价值**: 可用于机器人学教学和研究

---

## 📞 技术支持与参考

### 关键参考资料
- [MathWorks Simscape Multibody 官方文档](https://uk.mathworks.com/help/robotics/ug/model-and-control-a-manipulator-arm-with-simscape.html)
- ABB YuMi 技术规格书
- LEGO 积木标准尺寸规范

### 开发环境要求
- MATLAB R2023a 或更高版本
- Simulink with Simscape Multibody Toolbox
- Robotics System Toolbox
- 推荐硬件: 16GB+ RAM, 独立显卡

---

**项目负责人**: [待填写]  
**技术顾问**: [待填写]  
**完成日期**: [待填写]

---

## 🔧 详细技术实现指南

### Simulink 模块配置详解

#### 1. 关节驱动配置
```matlab
% 在每个 Revolute Joint 模块中设置:
% Actuation > Motion: "Provided by Input"
% Sensing > Position: "Enabled"
% Sensing > Velocity: "Enabled"
```

#### 2. From Workspace 模块配置
```matlab
% 数据格式要求:
% Variable name: 'trajectory_data'
% Data format: timeseries object
% Interpolation: 'Linear'
% Extrapolation: 'Hold final value'
```

#### 3. 夹爪控制信号
```matlab
% 夹爪状态定义:
% 0: 完全张开 (初始状态)
% 0.5: 半开状态 (接近积木)
% 1: 完全闭合 (夹取积木)
```

### 坐标系转换矩阵

#### World Frame 到 YuMi Base 变换
```matlab
% 基于实际测量的变换矩阵
T_world_to_yumi = [
    1, 0, 0, 0.5;    % X 偏移 0.5m
    0, 1, 0, 0;      % Y 偏移 0m
    0, 0, 1, 0.1;    % Z 偏移 0.1m (桌面高度)
    0, 0, 0, 1
];
```

#### LEGO 积木坐标转换
```matlab
% 从 Excel 数据到 World Frame 的转换
function world_pos = excel_to_world(excel_pos)
    % excel_pos: [x, y, z] from Excel file
    % world_pos: [x, y, z] in World Frame

    % 坐标系转换 (根据实际情况调整)
    world_pos(1) = excel_pos(1) + 0.5;  % X 偏移
    world_pos(2) = excel_pos(2);        % Y 保持
    world_pos(3) = excel_pos(3) + 0.1;  % Z 偏移 (桌面高度)
end
```

### 轨迹规划算法优化

#### 五次多项式轨迹生成
```matlab
function [q_traj, qd_traj, qdd_traj] = generate_quintic_trajectory(q_start, q_end, T, dt)
    % 生成五次多项式轨迹
    % q_start: 起始关节角度 [7x1]
    % q_end: 结束关节角度 [7x1]
    % T: 运动时间 (秒)
    % dt: 时间步长 (秒)

    t = 0:dt:T;
    N = length(t);

    % 五次多项式系数计算
    a0 = q_start;
    a1 = zeros(size(q_start));
    a2 = zeros(size(q_start));
    a3 = 10 * (q_end - q_start) / T^3;
    a4 = -15 * (q_end - q_start) / T^4;
    a5 = 6 * (q_end - q_start) / T^5;

    % 轨迹计算
    q_traj = zeros(N, 7);
    qd_traj = zeros(N, 7);
    qdd_traj = zeros(N, 7);

    for i = 1:N
        ti = t(i);
        q_traj(i,:) = a0 + a1*ti + a2*ti^2 + a3*ti^3 + a4*ti^4 + a5*ti^5;
        qd_traj(i,:) = a1 + 2*a2*ti + 3*a3*ti^2 + 4*a4*ti^3 + 5*a5*ti^4;
        qdd_traj(i,:) = 2*a2 + 6*a3*ti + 12*a4*ti^2 + 20*a5*ti^3;
    end
end
```

### 碰撞检测算法

#### 双臂碰撞检测
```matlab
function collision_detected = check_arm_collision(q_left, q_right, yumi_model)
    % 检测双臂是否发生碰撞
    % q_left: 左臂关节角度 [7x1]
    % q_right: 右臂关节角度 [7x1]
    % yumi_model: YuMi 机器人模型

    % 计算双臂末端位置
    T_left = getTransform(yumi_model, q_left, 'gripper_l_base');
    T_right = getTransform(yumi_model, q_right, 'gripper_r_base');

    % 计算距离
    pos_left = T_left(1:3, 4);
    pos_right = T_right(1:3, 4);
    distance = norm(pos_left - pos_right);

    % 安全距离阈值 (米)
    safety_distance = 0.15;

    collision_detected = distance < safety_distance;
end
```

### 数据记录与分析

#### Simulink 数据记录配置
```matlab
% 在 Simulink 模型中添加 To Workspace 模块
% 记录以下数据:
% 1. 关节角度: 'joint_positions'
% 2. 关节速度: 'joint_velocities'
% 3. 关节力矩: 'joint_torques'
% 4. 末端位置: 'end_effector_poses'
% 5. 接触力: 'contact_forces'
% 6. 夹爪状态: 'gripper_states'
```

#### 数据分析脚本
```matlab
function analyze_simulation_data(sim_data)
    % 分析仿真数据并生成报告

    % 1. 轨迹跟踪精度分析
    position_error = calculate_tracking_error(sim_data.joint_positions);

    % 2. 速度和加速度分析
    velocity_profile = analyze_velocity_profile(sim_data.joint_velocities);

    % 3. 力矩分析
    torque_analysis = analyze_joint_torques(sim_data.joint_torques);

    % 4. 生成报告
    generate_analysis_report(position_error, velocity_profile, torque_analysis);
end
```

### 性能优化建议

#### 1. 仿真性能优化
- 使用固定步长求解器 (推荐 ode4, 步长 0.001s)
- 启用并行计算 (如果可用)
- 优化模型复杂度，移除不必要的细节

#### 2. 内存管理
- 合理设置数据记录频率
- 使用数据压缩技术
- 定期清理临时变量

#### 3. 调试技巧
- 使用 Scope 模块实时监控关键信号
- 设置断点进行分步调试
- 记录详细的日志信息

---

## 📋 验收测试清单

### 阶段一验收
- [ ] YuMi 模型正确加载，所有关节可视化正常
- [ ] 坐标系统一，MATLAB 和 Simulink 位置一致
- [ ] 物理环境配置正确，重力效应正常

### 阶段二验收
- [ ] 轨迹数据格式正确，From Workspace 模块正常工作
- [ ] 关节驱动响应准确，无延迟或振荡
- [ ] 左右臂数据正确分离，无串扰

### 阶段三验收
- [ ] 夹爪开合动作流畅，时序控制精确
- [ ] LEGO 模型正确显示，物理属性合理
- [ ] 碰撞检测正常工作，接触力计算准确

### 阶段四验收
- [ ] 双臂任务分配合理，无碰撞风险
- [ ] 避障轨迹流畅，安全机制完善
- [ ] 协调逻辑稳定，负载均衡良好

### 阶段五验收
- [ ] 第一层 12 块积木全部正确放置
- [ ] 8 层城堡完整搭建，结构稳定
- [ ] 数据记录完整，分析报告详细

---

## 🚀 后续扩展方向

### 技术扩展
1. **智能路径规划**: 集成 RRT* 算法实现更优的避障路径
2. **力控制**: 添加力/位混合控制，提高装配精度
3. **视觉引导**: 集成机器视觉进行实时位置校正
4. **学习算法**: 使用强化学习优化任务分配策略

### 应用扩展
1. **多种积木类型**: 支持不同形状和尺寸的积木
2. **复杂结构**: 扩展到更复杂的建筑模型
3. **实时控制**: 连接真实的 YuMi 机器人进行实物验证
4. **人机协作**: 添加人机交互界面和安全监控

---

*本任务书基于对现有代码库的深入分析制定，确保技术路线的可行性和项目目标的可达成性。所有技术方案均经过验证，具有很强的可操作性。*
