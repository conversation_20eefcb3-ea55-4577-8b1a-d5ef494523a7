function runSimulink(trajectories, T_total)
    

    modelName = 'YumiSimscape';      
    open_system([modelName '.slx']); % 打開模型
     
   fprintf('\n=== 開始 Simulink 模擬 (%d 條軌跡, StopTime=%.1fs) ===\n',length(trajectories), T_total);

   for i = 1:length(trajectories)
        traj = trajectories{i};
        Q    = traj.Q_smooth;   % Q為平滑後軌跡
        N    = size(Q,1);
        T_total=10;
        % 時間向量／Sample time
        t_all = linspace(0, T_total, N)';   % N×1
        Ts = t_all(2) - t_all(1); %Sample time

        % 1) 根據是左臂/右臂，拆出關節序列

        % 取右／左臂的 7 个关节角度
        if strcmp(traj.arm,'right')
            qMatR = Q;            % N×7
            qMatL = zeros(N,7);   % 左臂静止
        else
            qMatR = zeros(N,7);   % 右臂静止
            qMatL = Q;            % N×7
        end

       trajDataRight = [t_all,qMatR];
       trajDataLeft = [t_all, qMatL];

        % --- 用 assign 寫入 base workspace --
        assignin('base', 'Ts',Ts);
        assignin('base', 'trajDataRight', trajDataRight);
        assignin('base', 'trajDataLeft',trajDataLeft);
       
        % % 設定 StopTime
        % set_param(modelName, 'StopTime', num2str(T_total));

 
        % 執行模擬
        fprintf('→ 任務 %2d/%2d (%5s)，%4d 點，Ts=%.4f... ', ...
                i, length(trajectories), traj.arm, N, Ts);
        sim(modelName);
        fprintf('Done\n');
    end

    fprintf('=== 全部模擬完成 ===\n\n');
end



%  try
%         % 檢查模型是否存在
%         if ~bdIsLoaded(modelName)
%             open_system([modelName '.slx']); % 打開模型
%             fprintf('✓ Simulink 模型 %s 已載入\n', modelName);
%         end
%     catch
%         error('❌ 無法載入 Simulink 模型: %s.slx', modelName);
%     end
%  % 
%     % 初始化雙臂軌跡數據
%     fprintf('\n=== 開始 Simulink 雙臂軌跡模擬 ===\n');
%     fprintf('總軌跡數: %d\n', length(trajectories));
%     fprintf('模擬總時間: %.1f 秒\n\n', T_total);
% 
%     for i = 1:length(trajectories)
%         traj = trajectories{i};
% 
%         % 檢查軌跡數據完整性
%         if isempty(traj.Q)
%             fprintf('⚠ 軌跡 %d 數據為空，跳過\n', i);
%             continue;
%         end
% 
%         % 使用平滑後的軌跡（如果存在）
%         if isfield(traj, 'Q_smooth') && ~isempty(traj.Q_smooth)
%             Q = traj.Q_smooth;
%         else
%             Q = traj.Q;
%         end
% 
%         N = size(Q, 1);
%         fprintf('【任務 %d/%d】%s 手臂 → %d 個軌跡點\n', ...
%             i, length(trajectories), traj.arm, N);
% 
%         % ===== 關鍵：正確提取左右手關節角度 =====
%         if strcmp(traj.arm, 'right')
%             % 右手：YuMi 的前7個關節 (1-7)
%             qMat = Q(:, 1:7);
%             fprintf('  使用右手關節 1-7\n');
%         else
%             % 左手：YuMi 的後7個關節 (8-14)
%             qMat = Q(:, 8:14);
%             fprintf('  使用左手關節 8-14\n');
%         end
% 
%         % ===== 生成時間向量 =====
%         t_all = linspace(0, T_total, N)';
% 
%         % ===== 組合軌跡數據 [時間, 關節角度] =====
%         trajData = [t_all, qMat];
% 
%         % 顯示數據信息
%         fprintf('  軌跡數據維度: %dx%d (時間+7關節)\n', size(trajData));
%         fprintf('  時間範圍: %.2f - %.2f 秒\n', t_all(1), t_all(end));
%         fprintf('  關節角度範圍: [%.3f, %.3f] 弧度\n', ...
%             min(qMat(:)), max(qMat(:)));
% 
%         % ===== 傳輸到 Base Workspace =====
%         try
%             assignin('base', 'trajData', trajData);
%             assignin('base', 'currentArm', traj.arm);
%             assignin('base', 'T_total', T_total);
% 
%             % 額外的軌跡信息
%             assignin('base', 'armName', traj.arm);
%             assignin('base', 'eeFrame', traj.eeName);
%             assignin('base', 'taskNumber', i);
% 
%             fprintf('  ✓ 軌跡數據已傳輸到 Base Workspace\n');
% 
%         catch ME
%             fprintf('  ❌ 數據傳輸失敗: %s\n', ME.message);
%             continue;
%         end
% 
%         % ===== 構造 SimulationInput =====
%         try
%             simIn = Simulink.SimulationInput(modelName);
% 
%             % 設置模擬參數
%             simIn = simIn.setModelParameter('StopTime', num2str(T_total));
%             simIn = simIn.setVariable('trajData', trajData);
%             simIn = simIn.setVariable('currentArm', traj.arm);
% 
%             fprintf('  🤖 開始模擬...');
%             tic;
% 
%             % 執行模擬
%             simOut = sim(simIn);
% 
%             elapsed = toc;
%             fprintf(' 完成 (%.1f 秒)\n', elapsed);
% 
%             % 可選：保存模擬結果
%             if nargout > 0 || i == 1  % 僅為示例
%                 % 提取關節角度數據（如果需要）
%                 try
%                     if strcmp(traj.arm, 'right')
%                         jointData = simOut.get('rightArmJoints');
%                     else
%                         jointData = simOut.get('leftArmJoints');
%                     end
%                     fprintf('  📊 模擬數據已提取\n');
%                 catch
%                     fprintf('  ⚠ 無法提取模擬數據\n');
%                 end
%             end
% 
%         catch ME
%             fprintf('  ❌ 模擬失敗: %s\n', ME.message);
%             fprintf('    錯誤位置: %s (第 %d 行)\n', ...
%                 ME.stack(1).name, ME.stack(1).line);
%             continue;
%         end
% 
%         % 任務間暫停
%         if i < length(trajectories)
%             fprintf('  ⏸ 等待下個任務...\n\n');
%             pause(1.0);
%         end
%     end
% 
%     fprintf('\n🎉 所有軌跡模擬完成！\n');
% 
%     % 清理 Base Workspace（可選）
%     try
%         evalin('base', 'clear trajData currentArm armName eeFrame taskNumber T_total');
%         fprintf('✓ Base Workspace 已清理\n');
%     catch
%         % 不影響主要功能
%     end
% end