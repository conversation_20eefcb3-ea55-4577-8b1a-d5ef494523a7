    clc; clear; close all;
    
    % 1. Robot + Environment Setup
    fprintf('Setting up robot environment...\n');
    [yumi, qHome, table, ax] = setupRobotEnv();
    
    
    % 2. LEGO Configuration
    fprintf('Loading LEGO configuration...\n');
    brick_config = lego_config();
    
    % 3. Trajectory Planning
    fprintf('Starting trajectory planning...\n');
    trajectories = planTrajectory(yumi, brick_config, qHome);
    
    
    % 4. Review Planned Trajectories
    fprintf('Trajectory planning complete: generated %d trajectories.\n', numel(trajectories));
    for i = 1:numel(trajectories)
        fprintf('Trajectory %d: %s arm, %d points\n', ...
            i, trajectories{i}.arm, size(trajectories{i}.Q, 1));
    end
    
    % 5.Simulink Simulation
    fprintf('Starting Simulink simulation...\n');
    T_total = 10; % 總模擬時間（秒）
   
    runSimulink(trajectories, T_total);
    
    
    % % 6. MATLAB Animation
    % fprintf('Starting construction animation...\n');
    % animateTrajectory(yumi, qHome, trajectories);
    % 
    
    
    % clc; clear; close all;
    % 
    % % 1. robot + env 
    % fprintf('set up robot environment');
    % [yumi, qHome, table, ax] = setupRobotEnv();
    % 
    % % 2. lego config 樂高配置
    % fprintf('load LEGO configuration');
    % brick_config = lego_config();
    % 
    % % 3. trajectory 軌跡規劃檔
    % fprintf('Start Trajectory planning...\n');
    % trajectories = planTrajectory(yumi, brick_config, qHome);
    % 
    % % 4. 檢查軌跡結果
    % fprintf('軌跡規劃完成，共生成 %d 個軌跡\n', length(trajectories));
    % for i = 1:length(trajectories)
    %     fprintf('軌跡 %d: %s 手臂, %d 個點\n', i, trajectories{i}.arm, size(trajectories{i}.Q, 1));
    % end
    % 
    % % %% 4. Simulink 模擬
    % % fprintf('開始 Simulink 模擬...\n');
    % % runSimulink(yumi, trajectories);
    % 
    % % 5. MATLAB animation 軌跡動畫
    % fprintf('開始城堡建築動畫...\n');
    % animateTrajectory(yumi, qHome, trajectories);
    % 
