%% YuMi LEGO Castle Stacking Simulation - Environment Verification Script
% Verify MATLAB environment, toolboxes and project files

fprintf('Starting YuMi LEGO Project Environment Verification...\n\n');

%% 1. MATLAB Version Check
fprintf('Step 1: MATLAB Version Check\n');
fprintf('MATLAB Version: %s\n', version);
matlab_version = version('-release');
fprintf('Release: %s\n', matlab_version);

% Check if version meets requirements (R2023a or higher)
required_year = 2023;
current_year = str2double(matlab_version(1:4));
if current_year >= required_year
    fprintf('✓ MATLAB version meets requirements\n\n');
else
    fprintf('✗ MATLAB version too old, need R2023a or higher\n\n');
end

%% 2. Required Toolbox Check
fprintf('Step 2: Toolbox Check\n');

% Check Robotics System Toolbox
if license('test', 'Robotics_System_Toolbox')
    fprintf('✓ Robotics System Toolbox: Installed\n');
else
    fprintf('✗ Robotics System Toolbox: Not installed\n');
end

% Check Simscape Multibody
if license('test', 'Simscape_Multibody')
    fprintf('✓ Simscape Multibody: Installed\n');
else
    fprintf('✗ Simscape Multibody: Not installed\n');
end

% Check Simulink
if license('test', 'Simulink')
    fprintf('✓ Simulink: Installed\n');
else
    fprintf('✗ Simulink: Not installed\n');
end

fprintf('\n');

%% 3. Project Files Check
fprintf('Step 3: Project Files Check\n');

% Check key MATLAB files
matlab_files = {
    'setupRobotEnv.m', 'Robot Environment Setup';
    'planTrajectory.m', 'Trajectory Planning';
    'lego_config.m', 'LEGO Configuration';
    'runSimulink.m', 'Simulink Execution';
    'setup_lego_and_run_sim.m', 'Main Simulation Script'
};

for i = 1:size(matlab_files, 1)
    filename = matlab_files{i, 1};
    description = matlab_files{i, 2};
    if exist(filename, 'file')
        fprintf('✓ %s (%s): Found\n', filename, description);
    else
        fprintf('✗ %s (%s): Missing\n', filename, description);
    end
end

% Check Simulink model files
simulink_files = {
    'YumiSimscape.slx', 'Main Simulation Model';
    'abbYumi_exported.slx', 'YuMi Exported Model'
};

for i = 1:size(simulink_files, 1)
    filename = simulink_files{i, 1};
    description = simulink_files{i, 2};
    if exist(filename, 'file')
        fprintf('✓ %s (%s): Found\n', filename, description);
    else
        fprintf('✗ %s (%s): Missing\n', filename, description);
    end
end

% Check URDF file
if exist('abbYumi_exported.urdf', 'file')
    fprintf('✓ abbYumi_exported.urdf (YuMi Robot Model): Found\n');
else
    fprintf('✗ abbYumi_exported.urdf (YuMi Robot Model): Missing\n');
end

fprintf('\n');

%% 4. YuMi Robot Model Test
fprintf('Step 4: YuMi Robot Model Test\n');

try
    % Try to load YuMi robot
    yumi = loadrobot('abbYumi', 'DataFormat', 'row', 'Gravity', [0 0 -9.81]);
    qHome = yumi.homeConfiguration;
    
    fprintf('✓ YuMi robot model loaded successfully\n');
    fprintf('   - Robot name: %s\n', yumi.Name);
    fprintf('   - Number of bodies: %d\n', yumi.NumBodies);
    fprintf('   - Degrees of freedom: %d\n', length(qHome));
    fprintf('   - Base position: [%.3f, %.3f, %.3f]\n', yumi.Base(1,4), yumi.Base(2,4), yumi.Base(3,4));
    
    % Check left and right arms
    try
        T_left = getTransform(yumi, qHome, 'gripper_l_base');
        T_right = getTransform(yumi, qHome, 'gripper_r_base');
        fprintf('   - Left arm end-effector: [%.3f, %.3f, %.3f]\n', T_left(1,4), T_left(2,4), T_left(3,4));
        fprintf('   - Right arm end-effector: [%.3f, %.3f, %.3f]\n', T_right(1,4), T_right(2,4), T_right(3,4));
        fprintf('✓ Dual-arm kinematics working\n');
    catch ME
        fprintf('✗ Dual-arm kinematics test failed: %s\n', ME.message);
    end
    
catch ME
    fprintf('✗ YuMi robot model loading failed: %s\n', ME.message);
end

fprintf('\n');

%% 5. Simulink Model Test
fprintf('Step 5: Simulink Model Test\n');

try
    % Check main simulation model
    if exist('YumiSimscape.slx', 'file')
        % Try to load model without opening
        load_system('YumiSimscape.slx');
        fprintf('✓ YumiSimscape.slx model loaded successfully\n');
        
        % Check model configuration
        configSet = getActiveConfigSet('YumiSimscape');
        solver = get_param(configSet, 'Solver');
        fprintf('   - Solver: %s\n', solver);
        
        % Close model
        close_system('YumiSimscape', 0);
    else
        fprintf('✗ YumiSimscape.slx model file does not exist\n');
    end
catch ME
    fprintf('✗ Simulink model test failed: %s\n', ME.message);
end

fprintf('\n');

%% 6. System Resources Check
fprintf('Step 6: System Resources Check\n');

% Check available memory
[user, sys] = memory;
total_memory_gb = user.MemAvailableAllArrays / 1024^3;
fprintf('Available memory: %.1f GB\n', total_memory_gb);

if total_memory_gb >= 16
    fprintf('✓ Sufficient memory (recommended 16GB+)\n');
elseif total_memory_gb >= 8
    fprintf('⚠ Moderate memory (recommend upgrading to 16GB+)\n');
else
    fprintf('✗ Insufficient memory (need at least 8GB)\n');
end

% Check MATLAB path
current_path = pwd;
fprintf('Current working directory: %s\n', current_path);

fprintf('\n');

%% 7. Generate Verification Report
fprintf('Step 7: Generate Verification Report\n');

% Create verification report structure
verification_report = struct();
verification_report.timestamp = datestr(now);
verification_report.matlab_version = version;
verification_report.matlab_release = matlab_version;
verification_report.working_directory = current_path;
verification_report.available_memory_gb = total_memory_gb;

% Save report
save('environment_verification_report.mat', 'verification_report');
fprintf('✓ Verification report saved: environment_verification_report.mat\n');

fprintf('\n');

%% 8. Summary and Recommendations
fprintf('Environment Verification Summary\n');
fprintf('=====================================\n');

% Check critical conditions
critical_checks = true;

% MATLAB version check
if current_year < required_year
    fprintf('✗ Need to upgrade MATLAB to R2023a or higher\n');
    critical_checks = false;
end

% Toolbox check
if ~license('test', 'Robotics_System_Toolbox')
    fprintf('✗ Need to install Robotics System Toolbox\n');
    critical_checks = false;
end

if ~license('test', 'Simscape_Multibody')
    fprintf('✗ Need to install Simscape Multibody\n');
    critical_checks = false;
end

if ~license('test', 'Simulink')
    fprintf('✗ Need to install Simulink\n');
    critical_checks = false;
end

% File check
if ~exist('setupRobotEnv.m', 'file')
    fprintf('✗ Missing critical file: setupRobotEnv.m\n');
    critical_checks = false;
end

if critical_checks
    fprintf('Environment verification PASSED! Ready to start development.\n');
    fprintf('\nNext steps:\n');
    fprintf('1. Run setupRobotEnv() to test robot environment\n');
    fprintf('2. Open YumiSimscape.slx to check Simulink model\n');
    fprintf('3. Start Phase 1: YuMi robot model verification and optimization\n');
else
    fprintf('Environment verification FAILED. Please resolve the above issues.\n');
end

fprintf('\nYuMi LEGO Project Environment Verification Complete!\n');
