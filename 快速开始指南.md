# YuMi LEGO 城堡堆叠仿真 - 快速开始指南

## 🚀 环境准备

### 必需软件
- **MATLAB R2023a** 或更高版本
- **Simulink** with Simscape Multibody Toolbox
- **Robotics System Toolbox**
- **Simscape Multibody Link** (用于 CAD 导入)

### 硬件要求
- **内存**: 16GB+ RAM (推荐 32GB)
- **显卡**: 独立显卡 (用于 3D 可视化)
- **存储**: 至少 10GB 可用空间

## 📁 项目文件结构

```
YuMi_LEGO_Project/
├── 说明/                          # 项目需求文档
│   ├── 说明要求.txt               # 项目需求说明
│   ├── 積木座標.csv(1).xlsx       # 积木坐标数据
│   ├── 第一層積木.csv.xlsx        # 第一层详细数据
│   ├── 1.jpg                     # 城堡设计图
│   └── 每一层样子/                # 各层设计图片
├── MATLAB代码/
│   ├── setupRobotEnv.m           # 机器人环境设置
│   ├── planTrajectory.m          # 轨迹规划
│   ├── lego_config.m             # LEGO配置
│   ├── runSimulink.m             # Simulink运行
│   └── setup_lego_and_run_sim.m  # 主仿真脚本
├── Simulink模型/
│   └── YumiSimscape.slx          # 主仿真模型
└── 数据文件/
    ├── abbYumi_exported.urdf     # YuMi机器人模型
    └── STL文件/                  # LEGO积木3D模型
```

## ⚡ 快速启动步骤

### 第一步：环境检查
```matlab
% 在 MATLAB 命令窗口运行
ver  % 检查工具箱版本

% 检查必需工具箱
license('test', 'Robotics_System_Toolbox')
license('test', 'Simscape_Multibody')
license('test', 'Simulink')
```

### 第二步：设置工作路径
```matlab
% 设置项目根目录为当前路径
cd('你的项目路径/YuMi_LEGO_Project')

% 添加所有子文件夹到路径
addpath(genpath(pwd))

% 保存路径设置
savepath
```

### 第三步：验证机器人模型
```matlab
% 运行环境设置脚本
setupRobotEnv

% 检查是否成功生成 URDF 文件
if exist('abbYumi_exported.urdf', 'file')
    disp('✅ YuMi 机器人模型已准备就绪')
else
    error('❌ YuMi 机器人模型加载失败')
end
```

### 第四步：测试基础功能
```matlab
% 测试轨迹规划功能
test_trajectory = planTrajectory();
if ~isempty(test_trajectory)
    disp('✅ 轨迹规划功能正常')
else
    error('❌ 轨迹规划功能异常')
end

% 测试 LEGO 配置
lego_config;
disp('✅ LEGO 配置加载完成')
```

### 第五步：运行简单仿真
```matlab
% 运行第一层积木的简单仿真
setup_lego_and_run_sim('layer', 1, 'blocks', 4);  % 仅测试4块积木

% 如果成功，会看到 Simulink 模型打开并开始仿真
```

## 🔧 常见问题解决

### 问题1：URDF 文件导入失败
```matlab
% 解决方案：重新生成 URDF 文件
delete('abbYumi_exported.urdf')  % 删除旧文件
setupRobotEnv  % 重新运行设置脚本
```

### 问题2：Simulink 模型打不开
```matlab
% 检查 Simulink 版本兼容性
ver simulink

% 如果版本过低，升级到 R2023a 或更高版本
```

### 问题3：坐标系不一致
```matlab
% 检查坐标变换设置
check_coordinate_system  % 运行坐标系检查脚本

% 手动设置坐标变换
T_world_to_yumi = [1,0,0,0.5; 0,1,0,0; 0,0,1,0.1; 0,0,0,1];
```

### 问题4：内存不足
```matlab
% 清理工作空间
clear all
close all
clc

% 减少仿真复杂度
set_param('YumiSimscape', 'MaxStep', '0.01')  % 增大时间步长
```

## 📊 验证检查清单

### 环境验证
- [ ] MATLAB R2023a+ 已安装
- [ ] 所有必需工具箱已激活
- [ ] 项目路径已正确设置
- [ ] 所有文件完整存在

### 功能验证
- [ ] YuMi 机器人模型成功加载
- [ ] URDF 文件正确生成
- [ ] 轨迹规划功能正常
- [ ] LEGO 配置正确加载
- [ ] Simulink 模型可以打开

### 仿真验证
- [ ] 简单仿真可以运行
- [ ] 机器人在 3D 环境中正确显示
- [ ] 关节运动正常
- [ ] 无错误或警告信息

## 🎯 下一步行动

完成快速启动后，请按照以下顺序进行：

1. **阅读详细任务书**: `YuMi_LEGO_城堡堆叠仿真项目_详细任务书.md`
2. **开始阶段一任务**: 仿真环境搭建与模型优化
3. **逐步完成各阶段**: 按照任务书的详细步骤执行
4. **记录问题和解决方案**: 建立项目日志

## 📞 技术支持

### 调试技巧
```matlab
% 启用详细日志
set_param(0, 'DiagnosticViewer', 'on')

% 检查模型配置
checkModel = 'YumiSimscape';
configSet = getActiveConfigSet(checkModel);
disp(configSet)

% 运行模型检查
Simulink.BlockDiagram.checkModel(checkModel)
```

### 性能监控
```matlab
% 监控内存使用
memory

% 监控仿真性能
tic
sim('YumiSimscape')
simulation_time = toc;
fprintf('仿真耗时: %.2f 秒\n', simulation_time)
```

### 日志记录
```matlab
% 创建日志文件
diary('simulation_log.txt')
diary on

% 你的仿真代码...

diary off
```

---

## 🎉 成功标志

当你看到以下现象时，说明快速启动成功：

1. ✅ MATLAB 命令窗口无错误信息
2. ✅ Simulink 模型正常打开并显示 YuMi 机器人
3. ✅ 3D 可视化窗口显示机器人和工作环境
4. ✅ 简单的关节运动测试正常执行
5. ✅ 所有验证检查项目都已完成

**恭喜！你已经成功完成了项目的快速启动。现在可以开始正式的开发工作了！** 🚀

---

*如果在快速启动过程中遇到任何问题，请参考详细任务书中的技术实现指南，或查看项目的故障排除文档。*
